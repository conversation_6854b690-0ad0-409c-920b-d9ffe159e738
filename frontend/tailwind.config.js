/** @type {import('tailwindcss').Config} */
import { theme } from './src/lib/theme';

module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      // Colors from our design system
      colors: {
        primary: theme.colors.primary,
        secondary: theme.colors.secondary,
        success: theme.colors.success,
        warning: theme.colors.warning,
        error: theme.colors.error,
        neutral: theme.colors.neutral,
        
        // Semantic color mappings
        background: {
          primary: 'var(--bg-primary)',
          secondary: 'var(--bg-secondary)',
          tertiary: 'var(--bg-tertiary)',
        },
        text: {
          primary: 'var(--text-primary)',
          secondary: 'var(--text-secondary)',
          tertiary: 'var(--text-tertiary)',
        },
        border: {
          primary: 'var(--border-primary)',
          secondary: 'var(--border-secondary)',
        },
      },
      
      // Typography
      fontFamily: theme.typography.fontFamily,
      fontSize: theme.typography.fontSize,
      fontWeight: theme.typography.fontWeight,
      
      // Spacing
      spacing: theme.spacing,
      
      // Border radius
      borderRadius: theme.borderRadius,
      
      // Box shadows
      boxShadow: theme.boxShadow,
      
      // Animation
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'fade-out': 'fadeOut 0.2s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'slide-out': 'slideOut 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'scale-out': 'scaleOut 0.2s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-subtle': 'bounceSubtle 1s ease-in-out infinite',
        'typing': 'typing 1.5s ease-in-out infinite',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        slideIn: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideOut: {
          '0%': { transform: 'translateY(0)', opacity: '1' },
          '100%': { transform: 'translateY(-10px)', opacity: '0' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        scaleOut: {
          '0%': { transform: 'scale(1)', opacity: '1' },
          '100%': { transform: 'scale(0.95)', opacity: '0' },
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-2px)' },
        },
        typing: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.3' },
        },
      },
      
      // Backdrop blur for glassmorphism
      backdropBlur: {
        xs: '2px',
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
        '2xl': '24px',
        '3xl': '40px',
      },
      
      // Custom gradients
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'gradient-mesh': 'linear-gradient(135deg, var(--tw-gradient-stops))',
        'glass-gradient': 'linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))',
      },
      
      // Z-index scale
      zIndex: {
        hide: '-1',
        auto: 'auto',
        base: '0',
        docked: '10',
        dropdown: '1000',
        sticky: '1100',
        banner: '1200',
        overlay: '1300',
        modal: '1400',
        popover: '1500',
        skipLink: '1600',
        toast: '1700',
        tooltip: '1800',
      },
      
      // Custom utilities for glassmorphism
      backgroundColor: {
        'glass-light': 'rgba(255, 255, 255, 0.1)',
        'glass-medium': 'rgba(255, 255, 255, 0.15)',
        'glass-strong': 'rgba(255, 255, 255, 0.2)',
        'glass-dark-light': 'rgba(0, 0, 0, 0.1)',
        'glass-dark-medium': 'rgba(0, 0, 0, 0.2)',
        'glass-dark-strong': 'rgba(0, 0, 0, 0.3)',
      },
      
      // Border colors for glassmorphism
      borderColor: {
        'glass': 'rgba(255, 255, 255, 0.2)',
        'glass-strong': 'rgba(255, 255, 255, 0.3)',
        'glass-dark': 'rgba(255, 255, 255, 0.1)',
      },
      
      // Responsive breakpoints
      screens: {
        'xs': '475px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
    },
  },
  plugins: [
    // Custom plugin for glassmorphism utilities
    function({ addUtilities, theme }) {
      const glassmorphismUtilities = {
        '.glass-light': {
          'background': 'rgba(255, 255, 255, 0.1)',
          'backdrop-filter': 'blur(10px)',
          'border': '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.glass-medium': {
          'background': 'rgba(255, 255, 255, 0.15)',
          'backdrop-filter': 'blur(15px)',
          'border': '1px solid rgba(255, 255, 255, 0.25)',
        },
        '.glass-strong': {
          'background': 'rgba(255, 255, 255, 0.2)',
          'backdrop-filter': 'blur(20px)',
          'border': '1px solid rgba(255, 255, 255, 0.3)',
        },
        '.glass-dark-light': {
          'background': 'rgba(0, 0, 0, 0.1)',
          'backdrop-filter': 'blur(10px)',
          'border': '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.glass-dark-medium': {
          'background': 'rgba(0, 0, 0, 0.2)',
          'backdrop-filter': 'blur(15px)',
          'border': '1px solid rgba(255, 255, 255, 0.15)',
        },
        '.glass-dark-strong': {
          'background': 'rgba(0, 0, 0, 0.3)',
          'backdrop-filter': 'blur(20px)',
          'border': '1px solid rgba(255, 255, 255, 0.2)',
        },
      };
      
      addUtilities(glassmorphismUtilities);
    },
    
    // Custom plugin for privacy indicators
    function({ addUtilities }) {
      const privacyUtilities = {
        '.privacy-encrypted': {
          'background': theme.colors.success[600],
          'color': 'white',
          'padding': '0.25rem 0.5rem',
          'border-radius': '0.375rem',
          'font-size': '0.75rem',
          'font-weight': '500',
        },
        '.privacy-unencrypted': {
          'background': theme.colors.warning[600],
          'color': 'white',
          'padding': '0.25rem 0.5rem',
          'border-radius': '0.375rem',
          'font-size': '0.75rem',
          'font-weight': '500',
        },
        '.privacy-verifying': {
          'background': theme.colors.neutral[600],
          'color': 'white',
          'padding': '0.25rem 0.5rem',
          'border-radius': '0.375rem',
          'font-size': '0.75rem',
          'font-weight': '500',
        },
      };
      
      addUtilities(privacyUtilities);
    },
  ],
};
