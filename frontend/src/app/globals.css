@import "tailwindcss";

/* CSS Variables for Dynamic Theming */
:root {
  /* Background Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #171717;
  --bg-tertiary: #262626;

  /* Text Colors */
  --text-primary: #fafafa;
  --text-secondary: #d4d4d4;
  --text-tertiary: #a3a3a3;

  /* Border Colors */
  --border-primary: #404040;
  --border-secondary: #525252;

  /* Accent Colors */
  --accent-primary: #6366f1;
  --accent-secondary: #d946ef;

  /* Status Colors */
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;

  /* Legacy variables for compatibility */
  --background: #0a0a0a;
  --foreground: #fafafa;
}

@theme inline {
  --color-background: var(--bg-primary);
  --color-foreground: var(--text-primary);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', 'Consolas', monospace;
}

/* Base Styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
}

body {
  background: linear-gradient(
    135deg,
    #1e1b4b 0%,
    #312e81 25%,
    #1e3a8a 50%,
    #1e40af 75%,
    #3730a3 100%
  );
  min-height: 100vh;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-secondary);
}

/* Glassmorphism Base Classes */
.glass-effect {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-card {
  @apply glass-effect rounded-xl shadow-xl;
}

.glass-button {
  @apply glass-effect rounded-lg px-4 py-2 transition-all duration-200;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

/* Message Bubble Styles */
.message-sent {
  @apply bg-primary-600 text-white rounded-2xl rounded-br-md px-4 py-2 max-w-xs ml-auto;
}

.message-received {
  @apply bg-neutral-800 text-neutral-100 rounded-2xl rounded-bl-md px-4 py-2 max-w-xs;
}

.message-system {
  @apply bg-neutral-900 text-neutral-400 rounded-lg px-3 py-2 text-sm text-center border border-neutral-700;
}

/* Privacy Indicator Styles */
.privacy-indicator {
  @apply inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium;
}

.privacy-encrypted {
  @apply bg-success-600 text-white;
}

.privacy-unencrypted {
  @apply bg-warning-600 text-white;
}

.privacy-verifying {
  @apply bg-neutral-600 text-white;
}

/* Animation Classes */
.animate-message-in {
  animation: messageSlideIn 0.3s ease-out;
}

.animate-typing {
  animation: typing 1.5s ease-in-out infinite;
}

.animate-pulse-subtle {
  animation: pulseSubtle 2s ease-in-out infinite;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes typing {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes pulseSubtle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Focus Styles for Accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-neutral-900;
}

/* Loading Spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-neutral-700 border-t-primary-500;
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #6366f1, #d946ef);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Responsive Typography */
.text-responsive-sm {
  @apply text-sm md:text-base;
}

.text-responsive-base {
  @apply text-base md:text-lg;
}

.text-responsive-lg {
  @apply text-lg md:text-xl;
}

.text-responsive-xl {
  @apply text-xl md:text-2xl;
}

/* Interactive Elements */
.interactive {
  @apply transition-all duration-200 ease-in-out;
}

.interactive:hover {
  @apply transform scale-105;
}

.interactive:active {
  @apply transform scale-95;
}

/* Status Indicators */
.status-online {
  @apply w-3 h-3 bg-success-500 rounded-full border-2 border-neutral-900;
}

.status-offline {
  @apply w-3 h-3 bg-neutral-500 rounded-full border-2 border-neutral-900;
}

.status-away {
  @apply w-3 h-3 bg-warning-500 rounded-full border-2 border-neutral-900;
}

/* Message Input Styles */
.message-input {
  @apply w-full bg-neutral-800 border border-neutral-700 rounded-lg px-4 py-3 text-neutral-100 placeholder-neutral-500 resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
}

.message-input:focus {
  @apply bg-neutral-750 border-primary-500;
}

/* Contact List Styles */
.contact-item {
  @apply p-4 border-b border-neutral-800 cursor-pointer transition-all duration-200 hover:bg-neutral-800/50;
}

.contact-item.active {
  @apply bg-neutral-800 border-l-4 border-l-primary-500;
}

.contact-item:hover {
  @apply bg-neutral-800/70;
}

/* Notification Badge */
.notification-badge {
  @apply absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium;
}

/* Modal Overlay */
.modal-overlay {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-modal;
}

.modal-content {
  @apply glass-card p-6 max-w-md w-full mx-4;
}

/* Tooltip */
.tooltip {
  @apply absolute z-tooltip bg-neutral-900 text-neutral-100 text-sm px-2 py-1 rounded shadow-lg;
}

/* Custom Utilities */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.border-gradient {
  border-image: linear-gradient(135deg, #6366f1, #d946ef) 1;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0a0a0a;
    --bg-secondary: #171717;
    --bg-tertiary: #262626;
    --text-primary: #fafafa;
    --text-secondary: #d4d4d4;
    --text-tertiary: #a3a3a3;
    --border-primary: #404040;
    --border-secondary: #525252;
  }
}
