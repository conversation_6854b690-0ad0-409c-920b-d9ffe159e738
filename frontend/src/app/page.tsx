'use client';

import { useState, useEffect } from 'react';
import { MessagingInterface } from '@/components/MessagingInterface';
import { WalletConnection } from '@/components/WalletConnection';
import { AztecProvider, useAztec } from '@/lib/aztec-context';

function AppContent() {
  const { isConnected } = useAztec();

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20" />
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-t from-black/20 to-transparent" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-4 py-8">
        {!isConnected ? (
          <div className="min-h-screen flex flex-col items-center justify-center">
            {/* Hero Section */}
            <header className="text-center mb-12 max-w-4xl">
              <div className="mb-6">
                <h1 className="text-6xl md:text-7xl font-bold text-white mb-4 tracking-tight">
                  <span className="gradient-text">MessAge</span>
                </h1>
                <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto rounded-full" />
              </div>

              <p className="text-xl md:text-2xl text-neutral-200 mb-4 font-light">
                Zero-Knowledge Private Messaging on Aztec
              </p>

              <p className="text-neutral-400 text-lg max-w-2xl mx-auto leading-relaxed">
                Your messages are encrypted client-side with zero-knowledge proofs.
                The blockchain never sees your content, ensuring complete privacy.
              </p>

              {/* Feature Highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 text-left">
                <div className="glass-card p-6 text-center">
                  <div className="w-12 h-12 bg-primary-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🔒</span>
                  </div>
                  <h3 className="text-white font-semibold mb-2">End-to-End Encrypted</h3>
                  <p className="text-neutral-400 text-sm">Messages encrypted with military-grade cryptography</p>
                </div>

                <div className="glass-card p-6 text-center">
                  <div className="w-12 h-12 bg-secondary-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <h3 className="text-white font-semibold mb-2">Zero-Knowledge Proofs</h3>
                  <p className="text-neutral-400 text-sm">Prove message authenticity without revealing content</p>
                </div>

                <div className="glass-card p-6 text-center">
                  <div className="w-12 h-12 bg-success-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🌐</span>
                  </div>
                  <h3 className="text-white font-semibold mb-2">Decentralized</h3>
                  <p className="text-neutral-400 text-sm">Built on Aztec blockchain for true decentralization</p>
                </div>
              </div>
            </header>

            <WalletConnection />
          </div>
        ) : (
          <MessagingInterface />
        )}
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <AztecProvider>
      <AppContent />
    </AztecProvider>
  );
}
