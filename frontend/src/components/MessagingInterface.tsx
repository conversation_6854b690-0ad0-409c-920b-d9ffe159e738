'use client';

import { useState, useEffect } from 'react';
import { useAztec } from '@/lib/aztec-context';
import { MessageComposer } from './MessageComposer';
import { MessageInbox } from './MessageInbox';
import { ContactList } from './ContactList';
import { KeyManager } from './KeyManager';
import { Button } from './ui/Button';
import { PrivacyIndicator } from './ui/PrivacyIndicator';
import { formatAddress, getInitials, generateAvatarColor } from '@/lib/utils';

export function MessagingInterface() {
  const { account, disconnect } = useAztec();
  const [activeTab, setActiveTab] = useState<'messages' | 'contacts' | 'keys'>('messages');
  const [selectedContact, setSelectedContact] = useState<string | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const userAddress = account?.getAddress().toString() || '';
  const userInitials = getInitials(userAddress);
  const avatarColor = generateAvatarColor(userAddress);

  const tabs = [
    {
      id: 'messages',
      label: 'Messages',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
        </svg>
      )
    },
    {
      id: 'contacts',
      label: 'Contacts',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      )
    },
    {
      id: 'keys',
      label: 'Privacy',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      )
    },
  ];

  return (
    <div className="h-screen flex flex-col max-w-7xl mx-auto">
      {/* Modern Header */}
      <header className="glass-card m-4 mb-0 p-4 border-b-0 rounded-b-none">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* User Avatar */}
            <div className={`w-12 h-12 ${avatarColor} rounded-full flex items-center justify-center relative`}>
              <span className="text-white font-bold text-lg">
                {userInitials}
              </span>
              <div className="absolute -bottom-1 -right-1">
                <div className="status-online" />
              </div>
            </div>

            {/* User Info */}
            <div className="hidden md:block">
              <h1 className="text-xl font-bold text-white gradient-text">
                MessAge
              </h1>
              <div className="flex items-center gap-2">
                <p className="text-neutral-400 text-sm font-mono">
                  {formatAddress(userAddress)}
                </p>
                <PrivacyIndicator status="encrypted" size="sm" showText={false} />
              </div>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center gap-3">
            {/* Mobile Menu Toggle */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 text-neutral-400 hover:text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            {/* Disconnect Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={disconnect}
              leftIcon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              }
            >
              <span className="hidden sm:inline">Disconnect</span>
            </Button>
          </div>
        </div>
      </header>

      {/* Modern Navigation */}
      <nav className="glass-card mx-4 mt-0 p-1 rounded-t-none border-t-0">
        <div className={`flex ${isMobileMenuOpen ? 'flex-col md:flex-row' : 'hidden md:flex'}`}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => {
                setActiveTab(tab.id as any);
                setIsMobileMenuOpen(false);
              }}
              className={`flex items-center gap-3 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-primary-600 text-white shadow-lg transform scale-105'
                  : 'text-neutral-400 hover:text-white hover:bg-white/10'
              }`}
            >
              {tab.icon}
              <span className="text-sm">{tab.label}</span>
              {activeTab === tab.id && (
                <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
              )}
            </button>
          ))}
        </div>
      </nav>

      {/* Main Content Area */}
      <main className="flex-1 flex overflow-hidden">
        {activeTab === 'messages' && (
          <div className="flex w-full h-full">
            {/* Contact Sidebar */}
            <div className="w-full md:w-80 lg:w-96 glass-card m-4 mr-0 md:mr-2 rounded-r-none md:rounded-r-xl border-r-0 md:border-r">
              <ContactList
                onSelectContact={setSelectedContact}
                selectedContact={selectedContact}
              />
            </div>

            {/* Message Area */}
            <div className="hidden md:flex flex-1 flex-col glass-card m-4 ml-2 rounded-l-none">
              {selectedContact ? (
                <>
                  <MessageInbox contactAddress={selectedContact} />
                  <MessageComposer recipientAddress={selectedContact} />
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center max-w-md">
                    <div className="w-24 h-24 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                      <svg className="w-12 h-12 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-2">
                      Start a Conversation
                    </h3>
                    <p className="text-neutral-400 leading-relaxed">
                      Select a contact from the sidebar to begin sending encrypted messages with zero-knowledge proofs.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'contacts' && (
          <div className="flex-1 glass-card m-4">
            <ContactList
              onSelectContact={setSelectedContact}
              selectedContact={selectedContact}
              showManagement={true}
            />
          </div>
        )}

        {activeTab === 'keys' && (
          <div className="flex-1 glass-card m-4">
            <div className="p-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Privacy & Security</h2>
                <p className="text-neutral-400">
                  Manage your encryption keys and privacy settings
                </p>
              </div>
              <KeyManager />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
