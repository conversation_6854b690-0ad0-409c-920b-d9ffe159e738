'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export type PrivacyStatus = 'encrypted' | 'unencrypted' | 'verifying' | 'error';

interface PrivacyIndicatorProps {
  status: PrivacyStatus;
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

const statusConfig = {
  encrypted: {
    icon: '🔒',
    text: 'Encrypted',
    description: 'Message is encrypted with zero-knowledge proof',
    className: 'privacy-encrypted',
    bgColor: 'bg-success-600',
    textColor: 'text-white',
  },
  unencrypted: {
    icon: '⚠️',
    text: 'Unencrypted',
    description: 'Message is not encrypted',
    className: 'privacy-unencrypted',
    bgColor: 'bg-warning-600',
    textColor: 'text-white',
  },
  verifying: {
    icon: '⏳',
    text: 'Verifying',
    description: 'Generating zero-knowledge proof...',
    className: 'privacy-verifying',
    bgColor: 'bg-neutral-600',
    textColor: 'text-white',
  },
  error: {
    icon: '❌',
    text: 'Error',
    description: 'Failed to encrypt message',
    className: 'privacy-error',
    bgColor: 'bg-error-600',
    textColor: 'text-white',
  },
};

const sizeConfig = {
  sm: {
    container: 'px-2 py-1 text-xs gap-1',
    icon: 'text-xs',
    text: 'text-xs',
  },
  md: {
    container: 'px-3 py-1.5 text-sm gap-1.5',
    icon: 'text-sm',
    text: 'text-sm',
  },
  lg: {
    container: 'px-4 py-2 text-base gap-2',
    icon: 'text-base',
    text: 'text-base',
  },
};

export function PrivacyIndicator({ 
  status, 
  className, 
  showText = true, 
  size = 'md',
  onClick 
}: PrivacyIndicatorProps) {
  const config = statusConfig[status];
  const sizeStyles = sizeConfig[size];
  
  const isClickable = !!onClick;

  return (
    <div
      className={cn(
        'inline-flex items-center rounded-full font-medium transition-all duration-200',
        config.bgColor,
        config.textColor,
        sizeStyles.container,
        isClickable && 'cursor-pointer hover:opacity-80 active:scale-95',
        className
      )}
      onClick={onClick}
      title={config.description}
      role={isClickable ? 'button' : undefined}
      tabIndex={isClickable ? 0 : undefined}
      onKeyDown={isClickable ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick?.();
        }
      } : undefined}
    >
      <span className={cn('flex-shrink-0', sizeStyles.icon)}>
        {status === 'verifying' ? (
          <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin" />
        ) : (
          config.icon
        )}
      </span>
      {showText && (
        <span className={cn('font-medium', sizeStyles.text)}>
          {config.text}
        </span>
      )}
    </div>
  );
}

// Hook for managing privacy status
export function usePrivacyStatus() {
  const [status, setStatus] = React.useState<PrivacyStatus>('unencrypted');
  const [isGeneratingProof, setIsGeneratingProof] = React.useState(false);

  const generateProof = React.useCallback(async () => {
    setIsGeneratingProof(true);
    setStatus('verifying');
    
    try {
      // Simulate ZK proof generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      setStatus('encrypted');
    } catch (error) {
      console.error('Failed to generate proof:', error);
      setStatus('error');
    } finally {
      setIsGeneratingProof(false);
    }
  }, []);

  const resetStatus = React.useCallback(() => {
    setStatus('unencrypted');
    setIsGeneratingProof(false);
  }, []);

  return {
    status,
    isGeneratingProof,
    generateProof,
    resetStatus,
    setStatus,
  };
}
