'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

interface MessageStatusProps {
  status: MessageStatus;
  timestamp?: Date;
  className?: string;
  showTimestamp?: boolean;
}

const statusConfig = {
  sending: {
    icon: (
      <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin" />
    ),
    color: 'text-neutral-400',
    description: 'Sending message...',
  },
  sent: {
    icon: (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
      </svg>
    ),
    color: 'text-neutral-400',
    description: 'Message sent',
  },
  delivered: {
    icon: (
      <div className="flex">
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
        <svg className="w-3 h-3 -ml-1" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      </div>
    ),
    color: 'text-neutral-400',
    description: 'Message delivered',
  },
  read: {
    icon: (
      <div className="flex">
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
        <svg className="w-3 h-3 -ml-1" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      </div>
    ),
    color: 'text-primary-400',
    description: 'Message read',
  },
  failed: {
    icon: (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
    ),
    color: 'text-error-400',
    description: 'Failed to send',
  },
};

function formatTime(date: Date): string {
  return date.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  });
}

export function MessageStatus({ 
  status, 
  timestamp, 
  className, 
  showTimestamp = true 
}: MessageStatusProps) {
  const config = statusConfig[status];

  return (
    <div className={cn(
      'flex items-center gap-1 text-xs',
      config.color,
      className
    )}>
      <span 
        className="flex-shrink-0" 
        title={config.description}
      >
        {config.icon}
      </span>
      {showTimestamp && timestamp && (
        <span className="text-xs">
          {formatTime(timestamp)}
        </span>
      )}
    </div>
  );
}

// Hook for managing message status
export function useMessageStatus() {
  const [status, setStatus] = React.useState<MessageStatus>('sending');
  const [timestamp, setTimestamp] = React.useState<Date>();

  const updateStatus = React.useCallback((newStatus: MessageStatus) => {
    setStatus(newStatus);
    if (newStatus !== 'sending') {
      setTimestamp(new Date());
    }
  }, []);

  const sendMessage = React.useCallback(async () => {
    setStatus('sending');
    setTimestamp(undefined);
    
    try {
      // Simulate message sending
      await new Promise(resolve => setTimeout(resolve, 1000));
      updateStatus('sent');
      
      // Simulate delivery
      setTimeout(() => updateStatus('delivered'), 2000);
      
      // Simulate read (optional)
      setTimeout(() => updateStatus('read'), 5000);
    } catch (error) {
      updateStatus('failed');
    }
  }, [updateStatus]);

  return {
    status,
    timestamp,
    updateStatus,
    sendMessage,
  };
}
