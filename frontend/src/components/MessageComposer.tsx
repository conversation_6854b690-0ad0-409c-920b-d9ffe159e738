'use client';

import { useState, useRef, useEffect } from 'react';
import { useAztec } from '@/lib/aztec-context';
import { encryptMessage, hashMessage } from '@/lib/crypto';
import { Button } from './ui/Button';
import { PrivacyIndicator, usePrivacyStatus } from './ui/PrivacyIndicator';
import { TypingIndicator, useTypingIndicator } from './ui/TypingIndicator';
import { MessageStatus, useMessageStatus } from './ui/MessageStatus';
import { debounce, isEmpty } from '@/lib/utils';

interface MessageComposerProps {
  recipientAddress: string;
}

export function MessageComposer({ recipientAddress }: MessageComposerProps) {
  const { account, pxe, contractAddress } = useAztec();
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Custom hooks for enhanced features
  const { status: privacyStatus, generateProof } = usePrivacyStatus();
  const { isTyping, startTyping, stopTyping } = useTypingIndicator();
  const { status: messageStatus, sendMessage: updateMessageStatus } = useMessageStatus();

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  // Debounced typing indicator
  const debouncedTyping = debounce(() => {
    stopTyping();
  }, 1000);

  const handleMessageChange = (value: string) => {
    setMessage(value);
    if (!isEmpty(value)) {
      startTyping();
      debouncedTyping();
    } else {
      stopTyping();
    }
  };

  const sendMessage = async () => {
    if (isEmpty(message) || !account || !pxe || !contractAddress) return;

    setIsSending(true);
    stopTyping();

    try {
      // Start message status tracking
      updateMessageStatus();

      // Generate ZK proof first
      await generateProof();

      // 1. Encrypt the message client-side
      const encryptedContent = await encryptMessage(message, recipientAddress);

      // 2. Create hash of the encrypted content
      const contentHash = hashMessage(encryptedContent);

      // 3. Send only the hash to the contract (not the actual message)
      console.log('Sending message hash to contract:', {
        recipient: recipientAddress,
        contentHash: contentHash.toString(),
        originalMessage: message, // This is NOT sent to the contract
      });

      // Simulate contract call
      await new Promise(resolve => setTimeout(resolve, 2000));

      setMessage('');
      setIsExpanded(false);

      // Success notification could be added here
    } catch (error) {
      console.error('Failed to send message:', error);
      // Error handling could be improved with toast notifications
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const maxLength = 1000;
  const isNearLimit = message.length > maxLength * 0.8;
  const isOverLimit = message.length > maxLength;

  return (
    <div className="border-t border-neutral-700 bg-neutral-900/50 backdrop-blur-sm">
      {/* Typing Indicator */}
      <TypingIndicator isVisible={isTyping} userName="You" />

      <div className="p-4">
        {/* Privacy Status Bar */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <PrivacyIndicator
              status={privacyStatus}
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            />
            {messageStatus !== 'sending' && (
              <MessageStatus
                status={messageStatus}
                showTimestamp={false}
              />
            )}
          </div>

          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-neutral-400 hover:text-white transition-colors p-1"
          >
            <svg
              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        {/* Expanded Privacy Info */}
        {isExpanded && (
          <div className="mb-4 p-3 bg-neutral-800/50 rounded-lg border border-neutral-700 animate-slide-in">
            <h4 className="text-sm font-medium text-white mb-2">Privacy Protection</h4>
            <ul className="text-xs text-neutral-400 space-y-1">
              <li>• Message encrypted client-side with AES-256</li>
              <li>• Zero-knowledge proof generated for authenticity</li>
              <li>• Only message hash stored on blockchain</li>
              <li>• Content never leaves your device unencrypted</li>
            </ul>
          </div>
        )}

        {/* Message Input Area */}
        <div className="flex items-end gap-3">
          <div className="flex-1">
            <div className="relative">
              <textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => handleMessageChange(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type your private message..."
                className={`message-input min-h-[44px] max-h-32 ${
                  isOverLimit ? 'border-error-500 focus:ring-error-500' : ''
                }`}
                disabled={isSending}
                maxLength={maxLength}
              />

              {/* Character Count */}
              <div className="absolute bottom-2 right-2">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  isOverLimit
                    ? 'bg-error-600 text-white'
                    : isNearLimit
                      ? 'bg-warning-600 text-white'
                      : 'bg-neutral-700 text-neutral-300'
                }`}>
                  {message.length}/{maxLength}
                </span>
              </div>
            </div>

            {/* Privacy Notice */}
            <div className="flex items-center gap-2 mt-2 text-xs text-neutral-500">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              <span>End-to-end encrypted • Zero-knowledge proof</span>
            </div>
          </div>

          {/* Send Button */}
          <Button
            onClick={sendMessage}
            disabled={isEmpty(message) || isSending || isOverLimit}
            isLoading={isSending}
            size="lg"
            className="mb-6"
            leftIcon={
              !isSending && (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              )
            }
          >
            {isSending ? 'Encrypting...' : 'Send'}
          </Button>
        </div>
      </div>
    </div>
  );
}
