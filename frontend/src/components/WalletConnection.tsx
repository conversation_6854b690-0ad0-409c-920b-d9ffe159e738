'use client';

import { useState } from 'react';
import { useAztec } from '@/lib/aztec-context';
import { Button } from '@/components/ui/Button';

export function WalletConnection() {
  const { connect } = useAztec();
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      await connect();
    } catch (error) {
      console.error('Connection failed:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <div className="max-w-lg mx-auto">
      <div className="glass-card p-8 text-center">
        <div className="mb-8">
          {/* Animated Icon */}
          <div className="relative w-20 h-20 mx-auto mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full animate-pulse-subtle" />
            <div className="relative w-full h-full bg-neutral-900 rounded-full flex items-center justify-center">
              <svg className="w-10 h-10 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
          </div>

          <h2 className="text-3xl font-bold text-white mb-3">
            Connect to Aztec
          </h2>
          <p className="text-neutral-300 text-lg leading-relaxed">
            Connect your wallet to the Aztec network and start sending
            <span className="text-primary-400 font-medium"> private messages</span> with
            zero-knowledge proofs.
          </p>
        </div>

        {/* Connection Steps */}
        <div className="mb-8 space-y-3 text-left">
          <div className="flex items-center gap-3 text-neutral-400">
            <div className="w-6 h-6 bg-primary-500/20 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-bold text-primary-400">1</span>
            </div>
            <span className="text-sm">Connect your Aztec-compatible wallet</span>
          </div>
          <div className="flex items-center gap-3 text-neutral-400">
            <div className="w-6 h-6 bg-primary-500/20 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-bold text-primary-400">2</span>
            </div>
            <span className="text-sm">Generate your encryption keys automatically</span>
          </div>
          <div className="flex items-center gap-3 text-neutral-400">
            <div className="w-6 h-6 bg-primary-500/20 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-bold text-primary-400">3</span>
            </div>
            <span className="text-sm">Start messaging with complete privacy</span>
          </div>
        </div>

        {/* Connect Button */}
        <Button
          onClick={handleConnect}
          disabled={isConnecting}
          isLoading={isConnecting}
          size="lg"
          className="w-full"
          leftIcon={
            !isConnecting && (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            )
          }
        >
          {isConnecting ? 'Connecting to Aztec...' : 'Connect Wallet'}
        </Button>

        {/* Security Notice */}
        <div className="mt-6 p-4 bg-neutral-800/50 rounded-lg border border-neutral-700">
          <div className="flex items-start gap-3">
            <div className="w-5 h-5 text-success-400 flex-shrink-0 mt-0.5">
              <svg fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="text-left">
              <p className="text-sm text-neutral-300 font-medium mb-1">
                Your privacy is guaranteed
              </p>
              <p className="text-xs text-neutral-400">
                All messages are encrypted locally before being sent. Your private keys never leave your device.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
