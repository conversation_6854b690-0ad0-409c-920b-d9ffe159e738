import React from 'react';
import { encryptMessage, hashMessage, generateMessageProof } from './crypto';
import { generateId } from './utils';

export interface Message {
  id: string;
  sender: string;
  recipient: string;
  content: string;
  encryptedContent: string;
  contentHash: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  isEncrypted: boolean;
  zkProof?: {
    proof: string;
    publicSignals: string[];
  };
  replyTo?: string;
  edited?: boolean;
  editedAt?: Date;
}

export interface Contact {
  address: string;
  name: string;
  publicKey: string;
  avatar?: string;
  lastSeen?: Date;
  isOnline: boolean;
  lastMessage?: Message;
  unreadCount: number;
}

export interface TypingUser {
  address: string;
  name: string;
  timestamp: Date;
}

// Message storage and management
class MessageStore {
  private messages: Map<string, Message[]> = new Map();
  private contacts: Map<string, Contact> = new Map();
  private typingUsers: Map<string, TypingUser> = new Map();
  private listeners: Set<(event: MessageEvent) => void> = new Set();

  // Event types
  private emit(event: MessageEvent) {
    this.listeners.forEach(listener => listener(event));
  }

  subscribe(listener: (event: MessageEvent) => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // Message operations
  async sendMessage(
    content: string,
    recipientAddress: string,
    senderAddress: string,
    senderPrivateKey: string
  ): Promise<Message> {
    const messageId = generateId();
    const timestamp = new Date();

    try {
      // Encrypt the message
      const encryptedContent = await encryptMessage(content, recipientAddress);
      const contentHash = hashMessage(encryptedContent);

      // Generate ZK proof
      const zkProof = await generateMessageProof(
        content,
        senderPrivateKey,
        recipientAddress
      );

      const message: Message = {
        id: messageId,
        sender: senderAddress,
        recipient: recipientAddress,
        content,
        encryptedContent,
        contentHash,
        timestamp,
        status: 'sending',
        isEncrypted: true,
        zkProof,
      };

      // Add to local storage
      this.addMessage(message);

      // Simulate sending to blockchain
      setTimeout(() => {
        message.status = 'sent';
        this.updateMessage(message);
      }, 1000);

      // Simulate delivery
      setTimeout(() => {
        message.status = 'delivered';
        this.updateMessage(message);
      }, 3000);

      return message;
    } catch (error) {
      const failedMessage: Message = {
        id: messageId,
        sender: senderAddress,
        recipient: recipientAddress,
        content,
        encryptedContent: '',
        contentHash: '',
        timestamp,
        status: 'failed',
        isEncrypted: false,
      };

      this.addMessage(failedMessage);
      throw error;
    }
  }

  private addMessage(message: Message) {
    const conversationKey = this.getConversationKey(message.sender, message.recipient);
    const messages = this.messages.get(conversationKey) || [];
    messages.push(message);
    this.messages.set(conversationKey, messages);

    this.emit({
      type: 'message_added',
      data: { message, conversationKey },
    });

    // Update contact's last message
    this.updateContactLastMessage(message);
  }

  private updateMessage(message: Message) {
    const conversationKey = this.getConversationKey(message.sender, message.recipient);
    const messages = this.messages.get(conversationKey) || [];
    const index = messages.findIndex(m => m.id === message.id);

    if (index !== -1) {
      messages[index] = message;
      this.messages.set(conversationKey, messages);

      this.emit({
        type: 'message_updated',
        data: { message, conversationKey },
      });
    }
  }

  private updateContactLastMessage(message: Message) {
    const contactAddress = message.sender === this.getCurrentUserAddress()
      ? message.recipient
      : message.sender;

    const contact = this.contacts.get(contactAddress);
    if (contact) {
      contact.lastMessage = message;
      if (message.sender !== this.getCurrentUserAddress()) {
        contact.unreadCount += 1;
      }
      this.contacts.set(contactAddress, contact);

      this.emit({
        type: 'contact_updated',
        data: { contact },
      });
    }
  }

  getMessages(userAddress: string, contactAddress: string): Message[] {
    const conversationKey = this.getConversationKey(userAddress, contactAddress);
    return this.messages.get(conversationKey) || [];
  }

  private getConversationKey(address1: string, address2: string): string {
    return [address1, address2].sort().join(':');
  }

  // Contact operations
  addContact(contact: Contact) {
    this.contacts.set(contact.address, contact);
    this.emit({
      type: 'contact_added',
      data: { contact },
    });
  }

  getContacts(): Contact[] {
    return Array.from(this.contacts.values());
  }

  getContact(address: string): Contact | undefined {
    return this.contacts.get(address);
  }

  updateContactOnlineStatus(address: string, isOnline: boolean) {
    const contact = this.contacts.get(address);
    if (contact) {
      contact.isOnline = isOnline;
      contact.lastSeen = isOnline ? undefined : new Date();
      this.contacts.set(address, contact);

      this.emit({
        type: 'contact_status_changed',
        data: { contact },
      });
    }
  }

  markMessagesAsRead(contactAddress: string) {
    const contact = this.contacts.get(contactAddress);
    if (contact) {
      contact.unreadCount = 0;
      this.contacts.set(contactAddress, contact);

      this.emit({
        type: 'messages_read',
        data: { contactAddress },
      });
    }
  }

  // Typing indicators
  setTyping(userAddress: string, userName: string, isTyping: boolean) {
    if (isTyping) {
      this.typingUsers.set(userAddress, {
        address: userAddress,
        name: userName,
        timestamp: new Date(),
      });
    } else {
      this.typingUsers.delete(userAddress);
    }

    this.emit({
      type: 'typing_changed',
      data: { userAddress, userName, isTyping },
    });
  }

  getTypingUsers(): TypingUser[] {
    // Clean up old typing indicators (older than 5 seconds)
    const now = new Date();
    for (const [address, user] of this.typingUsers.entries()) {
      if (now.getTime() - user.timestamp.getTime() > 5000) {
        this.typingUsers.delete(address);
      }
    }

    return Array.from(this.typingUsers.values());
  }

  // Utility methods
  private getCurrentUserAddress(): string {
    // This would be retrieved from the Aztec context
    return 'current_user_address';
  }

  // Search functionality
  searchMessages(query: string, contactAddress?: string): Message[] {
    const allMessages: Message[] = [];

    for (const messages of this.messages.values()) {
      allMessages.push(...messages);
    }

    return allMessages.filter(message => {
      const matchesQuery = message.content.toLowerCase().includes(query.toLowerCase());
      const matchesContact = !contactAddress ||
        message.sender === contactAddress ||
        message.recipient === contactAddress;

      return matchesQuery && matchesContact;
    });
  }

  // Export/Import for persistence
  exportData() {
    return {
      messages: Object.fromEntries(this.messages),
      contacts: Object.fromEntries(this.contacts),
    };
  }

  importData(data: any) {
    if (data.messages) {
      this.messages = new Map(Object.entries(data.messages));
    }
    if (data.contacts) {
      this.contacts = new Map(Object.entries(data.contacts));
    }
  }
}

// Event types
export type MessageEvent =
  | { type: 'message_added'; data: { message: Message; conversationKey: string } }
  | { type: 'message_updated'; data: { message: Message; conversationKey: string } }
  | { type: 'contact_added'; data: { contact: Contact } }
  | { type: 'contact_updated'; data: { contact: Contact } }
  | { type: 'contact_status_changed'; data: { contact: Contact } }
  | { type: 'messages_read'; data: { contactAddress: string } }
  | { type: 'typing_changed'; data: { userAddress: string; userName: string; isTyping: boolean } };

// Singleton instance
export const messageStore = new MessageStore();

// React hooks for messaging
export function useMessages(contactAddress: string) {
  const [messages, setMessages] = React.useState<Message[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const userAddress = 'current_user_address'; // Get from context
    const initialMessages = messageStore.getMessages(userAddress, contactAddress);
    setMessages(initialMessages);
    setIsLoading(false);

    const unsubscribe = messageStore.subscribe((event) => {
      if (event.type === 'message_added' || event.type === 'message_updated') {
        const updatedMessages = messageStore.getMessages(userAddress, contactAddress);
        setMessages(updatedMessages);
      }
    });

    return unsubscribe;
  }, [contactAddress]);

  return { messages, isLoading };
}

export function useContacts() {
  const [contacts, setContacts] = React.useState<Contact[]>([]);

  React.useEffect(() => {
    setContacts(messageStore.getContacts());

    const unsubscribe = messageStore.subscribe((event) => {
      if (event.type === 'contact_added' || event.type === 'contact_updated' || event.type === 'contact_status_changed') {
        setContacts(messageStore.getContacts());
      }
    });

    return unsubscribe;
  }, []);

  return contacts;
}
