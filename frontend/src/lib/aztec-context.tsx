'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

// Simplified types for demo purposes
interface MockAccount {
  getAddress: () => { toString: () => string };
}

interface AztecContextType {
  pxe: any | null;
  account: MockAccount | null;
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => void;
  contractAddress: string | null;
}

const AztecContext = createContext<AztecContextType | undefined>(undefined);

export function AztecProvider({ children }: { children: ReactNode }) {
  const [pxe, setPxe] = useState<any | null>(null);
  const [account, setAccount] = useState<MockAccount | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [contractAddress, setContractAddress] = useState<string | null>(null);

  const connect = async () => {
    try {
      // Simulate connection to Aztec sandbox
      console.log('Connecting to Aztec sandbox...');

      // Simulate delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create mock PXE client
      const mockPxe = {
        url: 'http://localhost:8080',
        isConnected: true,
      };
      setPxe(mockPxe);

      // Create mock account
      const mockAccount: MockAccount = {
        getAddress: () => ({
          toString: () => '0x' + Math.random().toString(16).slice(2, 42).padStart(40, '0')
        })
      };

      setAccount(mockAccount);
      setIsConnected(true);

      // Set mock contract address
      setContractAddress('0x' + Math.random().toString(16).slice(2, 42).padStart(40, '0'));

      console.log('Connected to Aztec network (simulated)');
    } catch (error) {
      console.error('Failed to connect to Aztec:', error);
      alert('Failed to connect to Aztec network. This is a demo version.');
    }
  };

  const disconnect = () => {
    setPxe(null);
    setAccount(null);
    setIsConnected(false);
    setContractAddress(null);
  };

  return (
    <AztecContext.Provider
      value={{
        pxe,
        account,
        isConnected,
        connect,
        disconnect,
        contractAddress,
      }}
    >
      {children}
    </AztecContext.Provider>
  );
}

export function useAztec() {
  const context = useContext(AztecContext);
  if (context === undefined) {
    throw new Error('useAztec must be used within an AztecProvider');
  }
  return context;
}
