[{"/Users/<USER>/Developer/mess-age/frontend/src/app/layout.tsx": "1", "/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx": "2", "/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx": "3", "/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx": "4", "/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx": "5", "/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx": "6", "/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx": "7", "/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx": "8", "/Users/<USER>/Developer/mess-age/frontend/src/components/ui/Button.tsx": "9", "/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx": "10", "/Users/<USER>/Developer/mess-age/frontend/src/components/ui/PrivacyIndicator.tsx": "11", "/Users/<USER>/Developer/mess-age/frontend/src/components/ui/TypingIndicator.tsx": "12", "/Users/<USER>/Developer/mess-age/frontend/src/lib/aztec-context.tsx": "13", "/Users/<USER>/Developer/mess-age/frontend/src/lib/crypto.ts": "14", "/Users/<USER>/Developer/mess-age/frontend/src/lib/messaging.ts": "15", "/Users/<USER>/Developer/mess-age/frontend/src/lib/theme.ts": "16", "/Users/<USER>/Developer/mess-age/frontend/src/lib/utils.ts": "17"}, {"size": 689, "mtime": 1748458494131, "results": "18", "hashOfConfig": "19"}, {"size": 3620, "mtime": 1748460539174, "results": "20", "hashOfConfig": "19"}, {"size": 10424, "mtime": 1748458955951, "results": "21", "hashOfConfig": "19"}, {"size": 10172, "mtime": 1748458998768, "results": "22", "hashOfConfig": "19"}, {"size": 7518, "mtime": 1748460458234, "results": "23", "hashOfConfig": "19"}, {"size": 5847, "mtime": 1748458911091, "results": "24", "hashOfConfig": "19"}, {"size": 8523, "mtime": 1748460382491, "results": "25", "hashOfConfig": "19"}, {"size": 4725, "mtime": 1748460145630, "results": "26", "hashOfConfig": "19"}, {"size": 2330, "mtime": 1748459879702, "results": "27", "hashOfConfig": "19"}, {"size": 4300, "mtime": 1748459981186, "results": "28", "hashOfConfig": "19"}, {"size": 3698, "mtime": 1748459942101, "results": "29", "hashOfConfig": "19"}, {"size": 1943, "mtime": 1748460583171, "results": "30", "hashOfConfig": "19"}, {"size": 2390, "mtime": 1748460268820, "results": "31", "hashOfConfig": "19"}, {"size": 6106, "mtime": 1748459027181, "results": "32", "hashOfConfig": "19"}, {"size": 9735, "mtime": 1748460291500, "results": "33", "hashOfConfig": "19"}, {"size": 7837, "mtime": 1748459723581, "results": "34", "hashOfConfig": "19"}, {"size": 4317, "mtime": 1748459902194, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fa9yb2", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Developer/mess-age/frontend/src/app/layout.tsx", [], [], "/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx", [], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx", ["87", "88"], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx", ["89", "90", "91"], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx", [], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx", ["92", "93"], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx", ["94", "95"], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx", [], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/ui/Button.tsx", [], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx", ["96"], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/ui/PrivacyIndicator.tsx", [], [], "/Users/<USER>/Developer/mess-age/frontend/src/components/ui/TypingIndicator.tsx", [], [], "/Users/<USER>/Developer/mess-age/frontend/src/lib/aztec-context.tsx", ["97", "98"], [], "/Users/<USER>/Developer/mess-age/frontend/src/lib/crypto.ts", ["99", "100", "101", "102", "103"], [], "/Users/<USER>/Developer/mess-age/frontend/src/lib/messaging.ts", ["104"], [], "/Users/<USER>/Developer/mess-age/frontend/src/lib/theme.ts", [], [], "/Users/<USER>/Developer/mess-age/frontend/src/lib/utils.ts", ["105", "106"], [], {"ruleId": "107", "severity": 2, "message": "108", "line": 22, "column": 20, "nodeType": null, "messageId": "109", "endLine": 22, "endColumn": 23}, {"ruleId": "110", "severity": 1, "message": "111", "line": 31, "column": 6, "nodeType": "112", "endLine": 31, "endColumn": 15, "suggestions": "113"}, {"ruleId": "107", "severity": 2, "message": "114", "line": 5, "column": 27, "nodeType": null, "messageId": "109", "endLine": 5, "endColumn": 42}, {"ruleId": "107", "severity": 2, "message": "108", "line": 14, "column": 20, "nodeType": null, "messageId": "109", "endLine": 14, "endColumn": 23}, {"ruleId": "110", "severity": 1, "message": "115", "line": 22, "column": 6, "nodeType": "112", "endLine": 22, "endColumn": 15, "suggestions": "116"}, {"ruleId": "110", "severity": 1, "message": "117", "line": 27, "column": 6, "nodeType": "112", "endLine": 27, "endColumn": 31, "suggestions": "118"}, {"ruleId": "107", "severity": 2, "message": "119", "line": 71, "column": 9, "nodeType": null, "messageId": "109", "endLine": 71, "endColumn": 30}, {"ruleId": "107", "severity": 2, "message": "120", "line": 3, "column": 20, "nodeType": null, "messageId": "109", "endLine": 3, "endColumn": 29}, {"ruleId": "121", "severity": 2, "message": "122", "line": 119, "column": 40, "nodeType": "123", "messageId": "124", "endLine": 119, "endColumn": 43, "suggestions": "125"}, {"ruleId": "107", "severity": 2, "message": "126", "line": 134, "column": 14, "nodeType": null, "messageId": "109", "endLine": 134, "endColumn": 19}, {"ruleId": "121", "severity": 2, "message": "122", "line": 11, "column": 8, "nodeType": "123", "messageId": "124", "endLine": 11, "endColumn": 11, "suggestions": "127"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 22, "column": 34, "nodeType": "123", "messageId": "124", "endLine": 22, "endColumn": 37, "suggestions": "128"}, {"ruleId": "107", "severity": 2, "message": "129", "line": 95, "column": 25, "nodeType": null, "messageId": "109", "endLine": 95, "endColumn": 40}, {"ruleId": "107", "severity": 2, "message": "130", "line": 95, "column": 42, "nodeType": null, "messageId": "109", "endLine": 95, "endColumn": 60}, {"ruleId": "107", "severity": 2, "message": "131", "line": 103, "column": 11, "nodeType": null, "messageId": "109", "endLine": 103, "endColumn": 29}, {"ruleId": "107", "severity": 2, "message": "132", "line": 167, "column": 3, "nodeType": null, "messageId": "109", "endLine": 167, "endColumn": 12}, {"ruleId": "107", "severity": 2, "message": "133", "line": 170, "column": 11, "nodeType": null, "messageId": "109", "endLine": 170, "endColumn": 22}, {"ruleId": "121", "severity": 2, "message": "122", "line": 293, "column": 20, "nodeType": "123", "messageId": "124", "endLine": 293, "endColumn": 23, "suggestions": "134"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 64, "column": 46, "nodeType": "123", "messageId": "124", "endLine": 64, "endColumn": 49, "suggestions": "135"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 64, "column": 56, "nodeType": "123", "messageId": "124", "endLine": 64, "endColumn": 59, "suggestions": "136"}, "@typescript-eslint/no-unused-vars", "'pxe' is assigned a value but never used.", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadContacts'. Either include it or remove the dependency array.", "ArrayExpression", ["137"], "'exportPublicKey' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadKeyPair'. Either include it or remove the dependency array.", ["138"], "React Hook useEffect has a missing dependency: 'loadMessages'. Either include it or remove the dependency array.", ["139"], "'decryptMessageContent' is assigned a value but never used.", "'useEffect' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["140", "141"], "'error' is defined but never used.", ["142", "143"], ["144", "145"], "'senderPub<PERSON><PERSON>ey' is assigned a value but never used.", "'recipient<PERSON><PERSON><PERSON><PERSON><PERSON>' is assigned a value but never used.", "'expectedCommitment' is assigned a value but never used.", "'publicKey' is defined but never used.", "'messageHash' is assigned a value but never used.", ["146", "147"], ["148", "149"], ["150", "151"], {"desc": "152", "fix": "153"}, {"desc": "154", "fix": "155"}, {"desc": "156", "fix": "157"}, {"messageId": "158", "fix": "159", "desc": "160"}, {"messageId": "161", "fix": "162", "desc": "163"}, {"messageId": "158", "fix": "164", "desc": "160"}, {"messageId": "161", "fix": "165", "desc": "163"}, {"messageId": "158", "fix": "166", "desc": "160"}, {"messageId": "161", "fix": "167", "desc": "163"}, {"messageId": "158", "fix": "168", "desc": "160"}, {"messageId": "161", "fix": "169", "desc": "163"}, {"messageId": "158", "fix": "170", "desc": "160"}, {"messageId": "161", "fix": "171", "desc": "163"}, {"messageId": "158", "fix": "172", "desc": "160"}, {"messageId": "161", "fix": "173", "desc": "163"}, "Update the dependencies array to be: [account, loadContacts]", {"range": "174", "text": "175"}, "Update the dependencies array to be: [account, loadKeyPair]", {"range": "176", "text": "177"}, "Update the dependencies array to be: [contactAddress, account, loadMessages]", {"range": "178", "text": "179"}, "suggestUnknown", {"range": "180", "text": "181"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "182", "text": "183"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "184", "text": "181"}, {"range": "185", "text": "183"}, {"range": "186", "text": "181"}, {"range": "187", "text": "183"}, {"range": "188", "text": "181"}, {"range": "189", "text": "183"}, {"range": "190", "text": "181"}, {"range": "191", "text": "183"}, {"range": "192", "text": "181"}, {"range": "193", "text": "183"}, [897, 906], "[account, loadContacts]", [644, 653], "[account, loadKeyPair]", [639, 664], "[contactAddress, account, loadMessages]", [4957, 4960], "unknown", [4957, 4960], "never", [244, 247], [244, 247], [588, 591], [588, 591], [7571, 7574], [7571, 7574], [1618, 1621], [1618, 1621], [1628, 1631], [1628, 1631]]