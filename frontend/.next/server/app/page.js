/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Developer/mess-age/frontend/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWJkdWxsYWhpYWJkaSUyRkRldmVsb3BlciUyRm1lc3MtYWdlJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWJkdWxsYWhpYWJkaS9EZXZlbG9wZXIvbWVzcy1hZ2UvZnJvbnRlbmQvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYmR1bGxhaGlhYmRpL0RldmVsb3Blci9tZXNzLWFnZS9mcm9udGVuZC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3201eee85b84\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWJkdWxsYWhpYWJkaS9EZXZlbG9wZXIvbWVzcy1hZ2UvZnJvbnRlbmQvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMyMDFlZWU4NWI4NFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYmR1bGxhaGlhYmRpL0RldmVsb3Blci9tZXNzLWFnZS9mcm9udGVuZC9zcmMvYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWJkdWxsYWhpYWJkaSUyRkRldmVsb3BlciUyRm1lc3MtYWdlJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWJkdWxsYWhpYWJkaS9EZXZlbG9wZXIvbWVzcy1hZ2UvZnJvbnRlbmQvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_MessagingInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/MessagingInterface */ \"(ssr)/./src/components/MessagingInterface.tsx\");\n/* harmony import */ var _components_WalletConnection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/WalletConnection */ \"(ssr)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AppContent() {\n    const { isConnected } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_4__.useAztec)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20 bg-gradient-to-t from-black/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col items-center justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"text-center mb-12 max-w-4xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-6xl md:text-7xl font-bold text-white mb-4 tracking-tight\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"gradient-text\",\n                                                children: \"MessAge\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl text-neutral-200 mb-4 font-light\",\n                                    children: \"Zero-Knowledge Private Messaging on Aztec\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-neutral-400 text-lg max-w-2xl mx-auto leading-relaxed\",\n                                    children: \"Your messages are encrypted client-side with zero-knowledge proofs. The blockchain never sees your content, ensuring complete privacy.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-card p-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-primary-500/20 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDD12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                        lineNumber: 44,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold mb-2\",\n                                                    children: \"End-to-End Encrypted\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-neutral-400 text-sm\",\n                                                    children: \"Messages encrypted with military-grade cryptography\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-card p-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-secondary-500/20 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold mb-2\",\n                                                    children: \"Zero-Knowledge Proofs\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-neutral-400 text-sm\",\n                                                    children: \"Prove message authenticity without revealing content\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-card p-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-success-500/20 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83C\\uDF10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold mb-2\",\n                                                    children: \"Decentralized\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-neutral-400 text-sm\",\n                                                    children: \"Built on Aztec blockchain for true decentralization\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WalletConnection__WEBPACK_IMPORTED_MODULE_3__.WalletConnection, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessagingInterface__WEBPACK_IMPORTED_MODULE_2__.MessagingInterface, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_aztec_context__WEBPACK_IMPORTED_MODULE_4__.AztecProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContent, {}, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUUwQjtBQUMyQztBQUNKO0FBQ0g7QUFFOUQsU0FBU0s7SUFDUCxNQUFNLEVBQUVDLFdBQVcsRUFBRSxHQUFHRiw0REFBUUE7SUFFaEMscUJBQ0UsOERBQUNHO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7OzBCQUlqQiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ1osQ0FBQ0YsNEJBQ0EsOERBQUNDO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0M7NEJBQU9ELFdBQVU7OzhDQUNoQiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRTs0Q0FBR0YsV0FBVTtzREFDWiw0RUFBQ0c7Z0RBQUtILFdBQVU7MERBQWdCOzs7Ozs7Ozs7OztzREFFbEMsOERBQUNEOzRDQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7OENBR2pCLDhEQUFDSTtvQ0FBRUosV0FBVTs4Q0FBdUQ7Ozs7Ozs4Q0FJcEUsOERBQUNJO29DQUFFSixXQUFVOzhDQUE2RDs7Ozs7OzhDQU0xRSw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRzt3REFBS0gsV0FBVTtrRUFBVzs7Ozs7Ozs7Ozs7OERBRTdCLDhEQUFDSztvREFBR0wsV0FBVTs4REFBZ0M7Ozs7Ozs4REFDOUMsOERBQUNJO29EQUFFSixXQUFVOzhEQUEyQjs7Ozs7Ozs7Ozs7O3NEQUcxQyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0c7d0RBQUtILFdBQVU7a0VBQVc7Ozs7Ozs7Ozs7OzhEQUU3Qiw4REFBQ0s7b0RBQUdMLFdBQVU7OERBQWdDOzs7Ozs7OERBQzlDLDhEQUFDSTtvREFBRUosV0FBVTs4REFBMkI7Ozs7Ozs7Ozs7OztzREFHMUMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNHO3dEQUFLSCxXQUFVO2tFQUFXOzs7Ozs7Ozs7Ozs4REFFN0IsOERBQUNLO29EQUFHTCxXQUFVOzhEQUFnQzs7Ozs7OzhEQUM5Qyw4REFBQ0k7b0RBQUVKLFdBQVU7OERBQTJCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzlDLDhEQUFDTiwwRUFBZ0JBOzs7Ozs7Ozs7O3lDQUduQiw4REFBQ0QsOEVBQWtCQTs7Ozs7Ozs7Ozs7Ozs7OztBQUs3QjtBQUVlLFNBQVNhO0lBQ3RCLHFCQUNFLDhEQUFDWCw2REFBYUE7a0JBQ1osNEVBQUNFOzs7Ozs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYmR1bGxhaGlhYmRpL0RldmVsb3Blci9tZXNzLWFnZS9mcm9udGVuZC9zcmMvYXBwL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IE1lc3NhZ2luZ0ludGVyZmFjZSB9IGZyb20gJ0AvY29tcG9uZW50cy9NZXNzYWdpbmdJbnRlcmZhY2UnO1xuaW1wb3J0IHsgV2FsbGV0Q29ubmVjdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy9XYWxsZXRDb25uZWN0aW9uJztcbmltcG9ydCB7IEF6dGVjUHJvdmlkZXIsIHVzZUF6dGVjIH0gZnJvbSAnQC9saWIvYXp0ZWMtY29udGV4dCc7XG5cbmZ1bmN0aW9uIEFwcENvbnRlbnQoKSB7XG4gIGNvbnN0IHsgaXNDb25uZWN0ZWQgfSA9IHVzZUF6dGVjKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgIHsvKiBBbmltYXRlZCBCYWNrZ3JvdW5kICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTkwMCB2aWEtYmx1ZS05MDAgdG8taW5kaWdvLTkwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgb3BhY2l0eS0yMCBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svMjAgdG8tdHJhbnNwYXJlbnRcIiAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICAgIHshaXNDb25uZWN0ZWQgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIHsvKiBIZXJvIFNlY3Rpb24gKi99XG4gICAgICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyIG1heC13LTR4bFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC02eGwgbWQ6dGV4dC03eGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNCB0cmFja2luZy10aWdodFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZ3JhZGllbnQtdGV4dFwiPk1lc3NBZ2U8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjQgaC0xIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wcmltYXJ5LTUwMCB0by1zZWNvbmRhcnktNTAwIG14LWF1dG8gcm91bmRlZC1mdWxsXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBtZDp0ZXh0LTJ4bCB0ZXh0LW5ldXRyYWwtMjAwIG1iLTQgZm9udC1saWdodFwiPlxuICAgICAgICAgICAgICAgIFplcm8tS25vd2xlZGdlIFByaXZhdGUgTWVzc2FnaW5nIG9uIEF6dGVjXG4gICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW5ldXRyYWwtNDAwIHRleHQtbGcgbWF4LXctMnhsIG14LWF1dG8gbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgWW91ciBtZXNzYWdlcyBhcmUgZW5jcnlwdGVkIGNsaWVudC1zaWRlIHdpdGggemVyby1rbm93bGVkZ2UgcHJvb2ZzLlxuICAgICAgICAgICAgICAgIFRoZSBibG9ja2NoYWluIG5ldmVyIHNlZXMgeW91ciBjb250ZW50LCBlbnN1cmluZyBjb21wbGV0ZSBwcml2YWN5LlxuICAgICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgICAgey8qIEZlYXR1cmUgSGlnaGxpZ2h0cyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02IG10LTEyIHRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3MtY2FyZCBwLTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLXByaW1hcnktNTAwLzIwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsXCI+8J+Ukjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBtYi0yXCI+RW5kLXRvLUVuZCBFbmNyeXB0ZWQ8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTQwMCB0ZXh0LXNtXCI+TWVzc2FnZXMgZW5jcnlwdGVkIHdpdGggbWlsaXRhcnktZ3JhZGUgY3J5cHRvZ3JhcGh5PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcy1jYXJkIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctc2Vjb25kYXJ5LTUwMC8yMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bFwiPuKaoTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBtYi0yXCI+WmVyby1Lbm93bGVkZ2UgUHJvb2ZzPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbmV1dHJhbC00MDAgdGV4dC1zbVwiPlByb3ZlIG1lc3NhZ2UgYXV0aGVudGljaXR5IHdpdGhvdXQgcmV2ZWFsaW5nIGNvbnRlbnQ8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWNhcmQgcC02IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1zdWNjZXNzLTUwMC8yMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bFwiPvCfjJA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgbWItMlwiPkRlY2VudHJhbGl6ZWQ8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTQwMCB0ZXh0LXNtXCI+QnVpbHQgb24gQXp0ZWMgYmxvY2tjaGFpbiBmb3IgdHJ1ZSBkZWNlbnRyYWxpemF0aW9uPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvaGVhZGVyPlxuXG4gICAgICAgICAgICA8V2FsbGV0Q29ubmVjdGlvbiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxNZXNzYWdpbmdJbnRlcmZhY2UgLz5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDxBenRlY1Byb3ZpZGVyPlxuICAgICAgPEFwcENvbnRlbnQgLz5cbiAgICA8L0F6dGVjUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJNZXNzYWdpbmdJbnRlcmZhY2UiLCJXYWxsZXRDb25uZWN0aW9uIiwiQXp0ZWNQcm92aWRlciIsInVzZUF6dGVjIiwiQXBwQ29udGVudCIsImlzQ29ubmVjdGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwiaGVhZGVyIiwiaDEiLCJzcGFuIiwicCIsImgzIiwiSG9tZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ContactList.tsx":
/*!****************************************!*\
  !*** ./src/components/ContactList.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactList: () => (/* binding */ ContactList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContactList auto */ \n\n\nfunction ContactList({ onSelectContact, selectedContact, showManagement = false }) {\n    const { account, pxe } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [contacts, setContacts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddContact, setShowAddContact] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newContactAddress, setNewContactAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newContactName, setNewContactName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactList.useEffect\": ()=>{\n            loadContacts();\n        }\n    }[\"ContactList.useEffect\"], [\n        account\n    ]);\n    const loadContacts = async ()=>{\n        if (!account) return;\n        setIsLoading(true);\n        try {\n            // In a real implementation, this would load contacts from local storage\n            // and verify their public keys from the contract\n            const simulatedContacts = [\n                {\n                    address: '0x1234567890abcdef1234567890abcdef12345678',\n                    name: 'Alice',\n                    publicKey: '0xabc123...',\n                    lastMessage: 'Hello! This is a private message.',\n                    lastMessageTime: new Date(Date.now() - 3600000),\n                    unreadCount: 2\n                },\n                {\n                    address: '0xfedcba0987654321fedcba0987654321fedcba09',\n                    name: 'Bob',\n                    publicKey: '0xdef456...',\n                    lastMessage: 'Thanks for the update!',\n                    lastMessageTime: new Date(Date.now() - 7200000),\n                    unreadCount: 0\n                },\n                {\n                    address: '0x1111222233334444555566667777888899990000',\n                    name: 'Charlie',\n                    publicKey: '0x789abc...',\n                    lastMessage: 'See you tomorrow',\n                    lastMessageTime: new Date(Date.now() - ********),\n                    unreadCount: 1\n                }\n            ];\n            setContacts(simulatedContacts);\n        } catch (error) {\n            console.error('Failed to load contacts:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const addContact = async ()=>{\n        if (!newContactAddress.trim() || !newContactName.trim()) return;\n        try {\n            // In a real implementation, this would:\n            // 1. Validate the address format\n            // 2. Check if the address has a registered public key on the contract\n            // 3. Add to local storage\n            const newContact = {\n                address: newContactAddress,\n                name: newContactName,\n                publicKey: '0x' + Math.random().toString(16).slice(2, 10) + '...',\n                unreadCount: 0\n            };\n            setContacts([\n                ...contacts,\n                newContact\n            ]);\n            setNewContactAddress('');\n            setNewContactName('');\n            setShowAddContact(false);\n        } catch (error) {\n            console.error('Failed to add contact:', error);\n            alert('Failed to add contact. Please check the address.');\n        }\n    };\n    const removeContact = (address)=>{\n        if (confirm('Are you sure you want to remove this contact?')) {\n            setContacts(contacts.filter((c)=>c.address !== address));\n            if (selectedContact === address) {\n                onSelectContact('');\n            }\n        }\n    };\n    const formatTime = (timestamp)=>{\n        if (!timestamp) return '';\n        const now = new Date();\n        const diff = now.getTime() - timestamp.getTime();\n        const minutes = Math.floor(diff / 60000);\n        const hours = Math.floor(diff / 3600000);\n        const days = Math.floor(diff / ********);\n        if (minutes < 1) return 'now';\n        if (minutes < 60) return `${minutes}m`;\n        if (hours < 24) return `${hours}h`;\n        return `${days}d`;\n    };\n    const truncateAddress = (address)=>{\n        return `${address.slice(0, 6)}...${address.slice(-4)}`;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 text-center text-gray-400\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-spin h-6 w-6 mx-auto mb-2\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                \"Loading contacts...\"\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-white font-semibold\",\n                            children: \"Contacts\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        showManagement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowAddContact(true),\n                            className: \"text-purple-400 hover:text-purple-300 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            showAddContact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-white/10 bg-white/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: \"Contact name\",\n                            value: newContactName,\n                            onChange: (e)=>setNewContactName(e.target.value),\n                            className: \"w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: \"Aztec address (0x...)\",\n                            value: newContactAddress,\n                            onChange: (e)=>setNewContactAddress(e.target.value),\n                            className: \"w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: addContact,\n                                    className: \"flex-1 bg-purple-600 hover:bg-purple-700 text-white text-sm py-2 px-3 rounded transition-colors\",\n                                    children: \"Add\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddContact(false),\n                                    className: \"flex-1 bg-gray-600 hover:bg-gray-700 text-white text-sm py-2 px-3 rounded transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: contacts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 text-center text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-12 h-12 mx-auto mb-4 opacity-50\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No contacts yet\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm mt-1\",\n                            children: \"Add contacts to start messaging\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this) : contacts.map((contact)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>onSelectContact(contact.address),\n                        className: `p-4 border-b border-white/10 cursor-pointer transition-colors hover:bg-white/5 ${selectedContact === contact.address ? 'bg-white/10' : ''}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-sm\",\n                                                children: contact.name.slice(0, 2).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-white font-medium truncate\",\n                                                            children: contact.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        contact.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400 ml-2\",\n                                                            children: formatTime(contact.lastMessageTime)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm truncate\",\n                                                    children: truncateAddress(contact.address)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this),\n                                                contact.lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs truncate mt-1\",\n                                                    children: contact.lastMessage\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        contact.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-purple-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\",\n                                            children: contact.unreadCount\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 21\n                                        }, this),\n                                        showManagement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                removeContact(contact.address);\n                                            },\n                                            className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 15\n                        }, this)\n                    }, contact.address, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ContactList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/KeyManager.tsx":
/*!***************************************!*\
  !*** ./src/components/KeyManager.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeyManager: () => (/* binding */ KeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* harmony import */ var _lib_crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/crypto */ \"(ssr)/./src/lib/crypto.ts\");\n/* __next_internal_client_entry_do_not_use__ KeyManager auto */ \n\n\n\nfunction KeyManager() {\n    const { account, pxe, contractAddress } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [keyPair, setKeyPair] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRegistering, setIsRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPrivateKey, setShowPrivateKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"KeyManager.useEffect\": ()=>{\n            loadKeyPair();\n        }\n    }[\"KeyManager.useEffect\"], [\n        account\n    ]);\n    const loadKeyPair = async ()=>{\n        if (!account) return;\n        try {\n            // In a real implementation, this would load from secure storage\n            // For demo, we'll generate a key pair\n            const keys = await (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.generateKeyPair)();\n            setKeyPair({\n                publicKey: keys.publicKey,\n                privateKey: keys.privateKey,\n                isRegistered: false\n            });\n        } catch (error) {\n            console.error('Failed to load key pair:', error);\n        }\n    };\n    const generateNewKeyPair = async ()=>{\n        setIsGenerating(true);\n        try {\n            const keys = await (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.generateKeyPair)();\n            setKeyPair({\n                publicKey: keys.publicKey,\n                privateKey: keys.privateKey,\n                isRegistered: false\n            });\n        } catch (error) {\n            console.error('Failed to generate key pair:', error);\n            alert('Failed to generate key pair');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const registerPublicKey = async ()=>{\n        if (!keyPair || !account || !contractAddress) return;\n        setIsRegistering(true);\n        try {\n            // In a real implementation, this would call the contract's register_public_key function\n            console.log('Registering public key:', keyPair.publicKey);\n            // Simulate contract call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            setKeyPair({\n                ...keyPair,\n                isRegistered: true\n            });\n            alert('Public key registered successfully!');\n        } catch (error) {\n            console.error('Failed to register public key:', error);\n            alert('Failed to register public key');\n        } finally{\n            setIsRegistering(false);\n        }\n    };\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n        alert('Copied to clipboard!');\n    };\n    const truncateKey = (key, length = 20)=>{\n        return `${key.slice(0, length)}...${key.slice(-length)}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-2\",\n                        children: \"Key Management\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Manage your encryption keys for private messaging\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            keyPair ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 rounded-lg p-6 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white\",\n                                children: \"Your Key Pair\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: keyPair.isRegistered ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                    children: \"Registered\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-yellow-500 text-black text-xs px-2 py-1 rounded-full\",\n                                    children: \"Not Registered\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300\",\n                                children: \"Public Key (Share this with contacts)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: truncateKey(keyPair.publicKey),\n                                        readOnly: true,\n                                        className: \"flex-1 bg-white/10 border border-white/20 rounded px-3 py-2 text-white text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>copyToClipboard(keyPair.publicKey),\n                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-sm transition-colors\",\n                                        children: \"Copy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300\",\n                                children: \"Private Key (Keep this secret!)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: showPrivateKey ? 'text' : 'password',\n                                        value: showPrivateKey ? truncateKey(keyPair.privateKey) : '••••••••••••••••••••',\n                                        readOnly: true,\n                                        className: \"flex-1 bg-white/10 border border-white/20 rounded px-3 py-2 text-white text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPrivateKey(!showPrivateKey),\n                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm transition-colors\",\n                                        children: showPrivateKey ? 'Hide' : 'Show'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>copyToClipboard(keyPair.privateKey),\n                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-sm transition-colors\",\n                                        children: \"Copy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3 pt-4\",\n                        children: [\n                            !keyPair.isRegistered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: registerPublicKey,\n                                disabled: isRegistering,\n                                className: \"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded transition-colors flex items-center\",\n                                children: isRegistering ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Registering...\"\n                                    ]\n                                }, void 0, true) : 'Register Public Key'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: generateNewKeyPair,\n                                disabled: isGenerating,\n                                className: \"bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 text-white px-4 py-2 rounded transition-colors flex items-center\",\n                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Generating...\"\n                                    ]\n                                }, void 0, true) : 'Generate New Keys'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"No Key Pair Found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 mb-4\",\n                        children: \"Generate a new key pair to start messaging\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: generateNewKeyPair,\n                        disabled: isGenerating,\n                        className: \"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-6 py-3 rounded-lg transition-colors flex items-center mx-auto\",\n                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-2 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generating...\"\n                            ]\n                        }, void 0, true) : 'Generate Key Pair'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 text-yellow-500 mt-0.5\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-500 font-semibold text-sm\",\n                                    children: \"Security Notice\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mt-1\",\n                                    children: \"Your private key is used to decrypt messages. Never share it with anyone. Store it securely and back it up safely.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/KeyManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessageComposer.tsx":
/*!********************************************!*\
  !*** ./src/components/MessageComposer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageComposer: () => (/* binding */ MessageComposer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* harmony import */ var _lib_crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/crypto */ \"(ssr)/./src/lib/crypto.ts\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ui_PrivacyIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/PrivacyIndicator */ \"(ssr)/./src/components/ui/PrivacyIndicator.tsx\");\n/* harmony import */ var _ui_TypingIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/TypingIndicator */ \"(ssr)/./src/components/ui/TypingIndicator.tsx\");\n/* harmony import */ var _ui_MessageStatus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/MessageStatus */ \"(ssr)/./src/components/ui/MessageStatus.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageComposer auto */ \n\n\n\n\n\n\n\n\nfunction MessageComposer({ recipientAddress }) {\n    const { account, pxe, contractAddress } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSending, setIsSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Custom hooks for enhanced features\n    const { status: privacyStatus, generateProof } = (0,_ui_PrivacyIndicator__WEBPACK_IMPORTED_MODULE_5__.usePrivacyStatus)();\n    const { isTyping, startTyping, stopTyping } = (0,_ui_TypingIndicator__WEBPACK_IMPORTED_MODULE_6__.useTypingIndicator)();\n    const { status: messageStatus, sendMessage: updateMessageStatus } = (0,_ui_MessageStatus__WEBPACK_IMPORTED_MODULE_7__.useMessageStatus)();\n    // Auto-resize textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageComposer.useEffect\": ()=>{\n            if (textareaRef.current) {\n                textareaRef.current.style.height = 'auto';\n                textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;\n            }\n        }\n    }[\"MessageComposer.useEffect\"], [\n        message\n    ]);\n    // Debounced typing indicator\n    const debouncedTyping = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.debounce)(()=>{\n        stopTyping();\n    }, 1000);\n    const handleMessageChange = (value)=>{\n        setMessage(value);\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.isEmpty)(value)) {\n            startTyping();\n            debouncedTyping();\n        } else {\n            stopTyping();\n        }\n    };\n    const sendMessage = async ()=>{\n        if ((0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.isEmpty)(message) || !account || !pxe || !contractAddress) return;\n        setIsSending(true);\n        stopTyping();\n        try {\n            // Start message status tracking\n            updateMessageStatus();\n            // Generate ZK proof first\n            await generateProof();\n            // 1. Encrypt the message client-side\n            const encryptedContent = await (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.encryptMessage)(message, recipientAddress);\n            // 2. Create hash of the encrypted content\n            const contentHash = (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.hashMessage)(encryptedContent);\n            // 3. Send only the hash to the contract (not the actual message)\n            console.log('Sending message hash to contract:', {\n                recipient: recipientAddress,\n                contentHash: contentHash.toString(),\n                originalMessage: message\n            });\n            // Simulate contract call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            setMessage('');\n            setIsExpanded(false);\n        // Success notification could be added here\n        } catch (error) {\n            console.error('Failed to send message:', error);\n        // Error handling could be improved with toast notifications\n        } finally{\n            setIsSending(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const maxLength = 1000;\n    const isNearLimit = message.length > maxLength * 0.8;\n    const isOverLimit = message.length > maxLength;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-t border-neutral-700 bg-neutral-900/50 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_TypingIndicator__WEBPACK_IMPORTED_MODULE_6__.TypingIndicator, {\n                isVisible: isTyping,\n                userName: \"You\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_PrivacyIndicator__WEBPACK_IMPORTED_MODULE_5__.PrivacyIndicator, {\n                                        status: privacyStatus,\n                                        size: \"sm\",\n                                        onClick: ()=>setIsExpanded(!isExpanded)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    messageStatus !== 'sending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_MessageStatus__WEBPACK_IMPORTED_MODULE_7__.MessageStatus, {\n                                        status: messageStatus,\n                                        showTimestamp: false\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsExpanded(!isExpanded),\n                                className: \"text-neutral-400 hover:text-white transition-colors p-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: `w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`,\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M19 9l-7 7-7-7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-3 bg-neutral-800/50 rounded-lg border border-neutral-700 animate-slide-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-white mb-2\",\n                                children: \"Privacy Protection\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-xs text-neutral-400 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Message encrypted client-side with AES-256\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Zero-knowledge proof generated for authenticity\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Only message hash stored on blockchain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Content never leaves your device unencrypted\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                ref: textareaRef,\n                                                value: message,\n                                                onChange: (e)=>handleMessageChange(e.target.value),\n                                                onKeyDown: handleKeyDown,\n                                                placeholder: \"Type your private message...\",\n                                                className: `message-input min-h-[44px] max-h-32 ${isOverLimit ? 'border-error-500 focus:ring-error-500' : ''}`,\n                                                disabled: isSending,\n                                                maxLength: maxLength\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-2 right-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs px-2 py-1 rounded-full ${isOverLimit ? 'bg-error-600 text-white' : isNearLimit ? 'bg-warning-600 text-white' : 'bg-neutral-700 text-neutral-300'}`,\n                                                    children: [\n                                                        message.length,\n                                                        \"/\",\n                                                        maxLength\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mt-2 text-xs text-neutral-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"End-to-end encrypted • Zero-knowledge proof\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: sendMessage,\n                                disabled: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.isEmpty)(message) || isSending || isOverLimit,\n                                isLoading: isSending,\n                                size: \"lg\",\n                                className: \"mb-6\",\n                                leftIcon: !isSending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 17\n                                }, void 0),\n                                children: isSending ? 'Encrypting...' : 'Send'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessageComposer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessageInbox.tsx":
/*!*****************************************!*\
  !*** ./src/components/MessageInbox.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageInbox: () => (/* binding */ MessageInbox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* harmony import */ var _lib_crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/crypto */ \"(ssr)/./src/lib/crypto.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageInbox auto */ \n\n\n\nfunction MessageInbox({ contactAddress }) {\n    const { account, pxe, contractAddress } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageInbox.useEffect\": ()=>{\n            loadMessages();\n        }\n    }[\"MessageInbox.useEffect\"], [\n        contactAddress,\n        account\n    ]);\n    const loadMessages = async ()=>{\n        if (!account || !pxe || !contractAddress) return;\n        setIsLoading(true);\n        try {\n            // In a real implementation, this would call the contract's get_messages function\n            // For now, we'll simulate some messages\n            const simulatedMessages = [\n                {\n                    id: '1',\n                    sender: contactAddress,\n                    content: 'Hello! This is a private message.',\n                    timestamp: new Date(Date.now() - 3600000),\n                    isDecrypted: true,\n                    contentHash: '0x1234...'\n                },\n                {\n                    id: '2',\n                    sender: account.getAddress().toString(),\n                    content: 'Hi there! Messages are encrypted end-to-end.',\n                    timestamp: new Date(Date.now() - 1800000),\n                    isDecrypted: true,\n                    contentHash: '0x5678...'\n                },\n                {\n                    id: '3',\n                    sender: contactAddress,\n                    content: 'Only you can decrypt and read this message!',\n                    timestamp: new Date(Date.now() - 900000),\n                    isDecrypted: true,\n                    contentHash: '0x9abc...'\n                }\n            ];\n            setMessages(simulatedMessages);\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const decryptMessageContent = async (messageHash)=>{\n        try {\n            // In a real implementation, this would:\n            // 1. Retrieve the encrypted content from off-chain storage using the hash\n            // 2. Decrypt it using the recipient's private key\n            const decryptedContent = await (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.decryptMessage)(messageHash, account?.getAddress().toString() || '');\n            return decryptedContent;\n        } catch (error) {\n            console.error('Failed to decrypt message:', error);\n            return 'Failed to decrypt message';\n        }\n    };\n    const formatTime = (timestamp)=>{\n        const now = new Date();\n        const diff = now.getTime() - timestamp.getTime();\n        const minutes = Math.floor(diff / 60000);\n        const hours = Math.floor(diff / 3600000);\n        const days = Math.floor(diff / ********);\n        if (minutes < 1) return 'Just now';\n        if (minutes < 60) return `${minutes}m ago`;\n        if (hours < 24) return `${hours}h ago`;\n        return `${days}d ago`;\n    };\n    const isOwnMessage = (sender)=>{\n        return sender === account?.getAddress().toString();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"animate-spin h-8 w-8 mx-auto mb-2\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                className: \"opacity-25\",\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                className: \"opacity-75\",\n                                fill: \"currentColor\",\n                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    \"Loading messages...\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n        children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center text-gray-400 mt-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-12 h-12 mx-auto mb-4 opacity-50\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No messages yet\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm mt-1\",\n                    children: \"Start a conversation!\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n            lineNumber: 118,\n            columnNumber: 9\n        }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex ${isOwnMessage(message.sender) ? 'justify-end' : 'justify-start'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${isOwnMessage(message.sender) ? 'bg-purple-600 text-white' : 'bg-white/10 text-white'}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs opacity-75\",\n                                    children: isOwnMessage(message.sender) ? 'You' : 'Contact'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs opacity-75 ml-2\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 15\n                        }, this),\n                        message.isDecrypted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mt-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-3 h-3 text-green-400 mr-1\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs opacity-75\",\n                                    children: \"Decrypted\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 13\n                }, this)\n            }, message.id, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                lineNumber: 127,\n                columnNumber: 11\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9NZXNzYWdlSW5ib3gudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0c7QUFDRDtBQWV2QyxTQUFTSSxhQUFhLEVBQUVDLGNBQWMsRUFBcUI7SUFDaEUsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLEdBQUcsRUFBRUMsZUFBZSxFQUFFLEdBQUdOLDREQUFRQTtJQUNsRCxNQUFNLENBQUNPLFVBQVVDLFlBQVksR0FBR1YsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUNXLFdBQVdDLGFBQWEsR0FBR1osK0NBQVFBLENBQUM7SUFFM0NDLGdEQUFTQTtrQ0FBQztZQUNSWTtRQUNGO2lDQUFHO1FBQUNSO1FBQWdCQztLQUFRO0lBRTVCLE1BQU1PLGVBQWU7UUFDbkIsSUFBSSxDQUFDUCxXQUFXLENBQUNDLE9BQU8sQ0FBQ0MsaUJBQWlCO1FBRTFDSSxhQUFhO1FBQ2IsSUFBSTtZQUNGLGlGQUFpRjtZQUNqRix3Q0FBd0M7WUFDeEMsTUFBTUUsb0JBQStCO2dCQUNuQztvQkFDRUMsSUFBSTtvQkFDSkMsUUFBUVg7b0JBQ1JZLFNBQVM7b0JBQ1RDLFdBQVcsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLO29CQUNqQ0MsYUFBYTtvQkFDYkMsYUFBYTtnQkFDZjtnQkFDQTtvQkFDRVAsSUFBSTtvQkFDSkMsUUFBUVYsUUFBUWlCLFVBQVUsR0FBR0MsUUFBUTtvQkFDckNQLFNBQVM7b0JBQ1RDLFdBQVcsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLO29CQUNqQ0MsYUFBYTtvQkFDYkMsYUFBYTtnQkFDZjtnQkFDQTtvQkFDRVAsSUFBSTtvQkFDSkMsUUFBUVg7b0JBQ1JZLFNBQVM7b0JBQ1RDLFdBQVcsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLO29CQUNqQ0MsYUFBYTtvQkFDYkMsYUFBYTtnQkFDZjthQUNEO1lBRURaLFlBQVlJO1FBQ2QsRUFBRSxPQUFPVyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1FBQzVDLFNBQVU7WUFDUmIsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNZSx3QkFBd0IsT0FBT0M7UUFDbkMsSUFBSTtZQUNGLHdDQUF3QztZQUN4QywwRUFBMEU7WUFDMUUsa0RBQWtEO1lBQ2xELE1BQU1DLG1CQUFtQixNQUFNMUIsMkRBQWNBLENBQUN5QixhQUFhdEIsU0FBU2lCLGFBQWFDLGNBQWM7WUFDL0YsT0FBT0s7UUFDVCxFQUFFLE9BQU9KLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUMsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNSyxhQUFhLENBQUNaO1FBQ2xCLE1BQU1FLE1BQU0sSUFBSUQ7UUFDaEIsTUFBTVksT0FBT1gsSUFBSVksT0FBTyxLQUFLZCxVQUFVYyxPQUFPO1FBQzlDLE1BQU1DLFVBQVVDLEtBQUtDLEtBQUssQ0FBQ0osT0FBTztRQUNsQyxNQUFNSyxRQUFRRixLQUFLQyxLQUFLLENBQUNKLE9BQU87UUFDaEMsTUFBTU0sT0FBT0gsS0FBS0MsS0FBSyxDQUFDSixPQUFPO1FBRS9CLElBQUlFLFVBQVUsR0FBRyxPQUFPO1FBQ3hCLElBQUlBLFVBQVUsSUFBSSxPQUFPLEdBQUdBLFFBQVEsS0FBSyxDQUFDO1FBQzFDLElBQUlHLFFBQVEsSUFBSSxPQUFPLEdBQUdBLE1BQU0sS0FBSyxDQUFDO1FBQ3RDLE9BQU8sR0FBR0MsS0FBSyxLQUFLLENBQUM7SUFDdkI7SUFFQSxNQUFNQyxlQUFlLENBQUN0QjtRQUNwQixPQUFPQSxXQUFXVixTQUFTaUIsYUFBYUM7SUFDMUM7SUFFQSxJQUFJYixXQUFXO1FBQ2IscUJBQ0UsOERBQUM0QjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFJRCxXQUFVO3dCQUFvQ0UsT0FBTTt3QkFBNkJDLE1BQUs7d0JBQU9DLFNBQVE7OzBDQUN4Ryw4REFBQ0M7Z0NBQU9MLFdBQVU7Z0NBQWFNLElBQUc7Z0NBQUtDLElBQUc7Z0NBQUtDLEdBQUU7Z0NBQUtDLFFBQU87Z0NBQWVDLGFBQVk7Ozs7OzswQ0FDeEYsOERBQUNDO2dDQUFLWCxXQUFVO2dDQUFhRyxNQUFLO2dDQUFlUyxHQUFFOzs7Ozs7Ozs7Ozs7b0JBQy9DOzs7Ozs7Ozs7Ozs7SUFLZDtJQUVBLHFCQUNFLDhEQUFDYjtRQUFJQyxXQUFVO2tCQUNaL0IsU0FBUzRDLE1BQU0sS0FBSyxrQkFDbkIsOERBQUNkO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBSUQsV0FBVTtvQkFBb0NHLE1BQUs7b0JBQU9NLFFBQU87b0JBQWVMLFNBQVE7OEJBQzNGLDRFQUFDTzt3QkFBS0csZUFBYzt3QkFBUUMsZ0JBQWU7d0JBQVFMLGFBQWE7d0JBQUdFLEdBQUU7Ozs7Ozs7Ozs7OzhCQUV2RSw4REFBQ0k7OEJBQUU7Ozs7Ozs4QkFDSCw4REFBQ0E7b0JBQUVoQixXQUFVOzhCQUFlOzs7Ozs7Ozs7OzttQkFHOUIvQixTQUFTZ0QsR0FBRyxDQUFDLENBQUNDLHdCQUNaLDhEQUFDbkI7Z0JBRUNDLFdBQVcsQ0FBQyxLQUFLLEVBQUVGLGFBQWFvQixRQUFRMUMsTUFBTSxJQUFJLGdCQUFnQixpQkFBaUI7MEJBRW5GLDRFQUFDdUI7b0JBQ0NDLFdBQVcsQ0FBQywwQ0FBMEMsRUFDcERGLGFBQWFvQixRQUFRMUMsTUFBTSxJQUN2Qiw2QkFDQSwwQkFDSjs7c0NBRUYsOERBQUN1Qjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNtQjtvQ0FBS25CLFdBQVU7OENBQ2JGLGFBQWFvQixRQUFRMUMsTUFBTSxJQUFJLFFBQVE7Ozs7Ozs4Q0FFMUMsOERBQUMyQztvQ0FBS25CLFdBQVU7OENBQ2JWLFdBQVc0QixRQUFReEMsU0FBUzs7Ozs7Ozs7Ozs7O3NDQUdqQyw4REFBQ3NDOzRCQUFFaEIsV0FBVTtzQ0FBV2tCLFFBQVF6QyxPQUFPOzs7Ozs7d0JBQ3RDeUMsUUFBUXJDLFdBQVcsa0JBQ2xCLDhEQUFDa0I7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTtvQ0FBOEJHLE1BQUs7b0NBQWVDLFNBQVE7OENBQ3ZFLDRFQUFDTzt3Q0FBS1MsVUFBUzt3Q0FBVVIsR0FBRTt3Q0FBd0lTLFVBQVM7Ozs7Ozs7Ozs7OzhDQUU5Syw4REFBQ0Y7b0NBQUtuQixXQUFVOzhDQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2VBeEJ0Q2tCLFFBQVEzQyxFQUFFOzs7Ozs7Ozs7O0FBaUMzQiIsInNvdXJjZXMiOlsiL1VzZXJzL2FiZHVsbGFoaWFiZGkvRGV2ZWxvcGVyL21lc3MtYWdlL2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL01lc3NhZ2VJbmJveC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlQXp0ZWMgfSBmcm9tICdAL2xpYi9henRlYy1jb250ZXh0JztcbmltcG9ydCB7IGRlY3J5cHRNZXNzYWdlIH0gZnJvbSAnQC9saWIvY3J5cHRvJztcblxuaW50ZXJmYWNlIE1lc3NhZ2Uge1xuICBpZDogc3RyaW5nO1xuICBzZW5kZXI6IHN0cmluZztcbiAgY29udGVudDogc3RyaW5nO1xuICB0aW1lc3RhbXA6IERhdGU7XG4gIGlzRGVjcnlwdGVkOiBib29sZWFuO1xuICBjb250ZW50SGFzaDogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgTWVzc2FnZUluYm94UHJvcHMge1xuICBjb250YWN0QWRkcmVzczogc3RyaW5nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gTWVzc2FnZUluYm94KHsgY29udGFjdEFkZHJlc3MgfTogTWVzc2FnZUluYm94UHJvcHMpIHtcbiAgY29uc3QgeyBhY2NvdW50LCBweGUsIGNvbnRyYWN0QWRkcmVzcyB9ID0gdXNlQXp0ZWMoKTtcbiAgY29uc3QgW21lc3NhZ2VzLCBzZXRNZXNzYWdlc10gPSB1c2VTdGF0ZTxNZXNzYWdlW10+KFtdKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWRNZXNzYWdlcygpO1xuICB9LCBbY29udGFjdEFkZHJlc3MsIGFjY291bnRdKTtcblxuICBjb25zdCBsb2FkTWVzc2FnZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFhY2NvdW50IHx8ICFweGUgfHwgIWNvbnRyYWN0QWRkcmVzcykgcmV0dXJuO1xuXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICAvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY2FsbCB0aGUgY29udHJhY3QncyBnZXRfbWVzc2FnZXMgZnVuY3Rpb25cbiAgICAgIC8vIEZvciBub3csIHdlJ2xsIHNpbXVsYXRlIHNvbWUgbWVzc2FnZXNcbiAgICAgIGNvbnN0IHNpbXVsYXRlZE1lc3NhZ2VzOiBNZXNzYWdlW10gPSBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzEnLFxuICAgICAgICAgIHNlbmRlcjogY29udGFjdEFkZHJlc3MsXG4gICAgICAgICAgY29udGVudDogJ0hlbGxvISBUaGlzIGlzIGEgcHJpdmF0ZSBtZXNzYWdlLicsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMzYwMDAwMCksXG4gICAgICAgICAgaXNEZWNyeXB0ZWQ6IHRydWUsXG4gICAgICAgICAgY29udGVudEhhc2g6ICcweDEyMzQuLi4nLFxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgICBzZW5kZXI6IGFjY291bnQuZ2V0QWRkcmVzcygpLnRvU3RyaW5nKCksXG4gICAgICAgICAgY29udGVudDogJ0hpIHRoZXJlISBNZXNzYWdlcyBhcmUgZW5jcnlwdGVkIGVuZC10by1lbmQuJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKERhdGUubm93KCkgLSAxODAwMDAwKSxcbiAgICAgICAgICBpc0RlY3J5cHRlZDogdHJ1ZSxcbiAgICAgICAgICBjb250ZW50SGFzaDogJzB4NTY3OC4uLicsXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzMnLFxuICAgICAgICAgIHNlbmRlcjogY29udGFjdEFkZHJlc3MsXG4gICAgICAgICAgY29udGVudDogJ09ubHkgeW91IGNhbiBkZWNyeXB0IGFuZCByZWFkIHRoaXMgbWVzc2FnZSEnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDkwMDAwMCksXG4gICAgICAgICAgaXNEZWNyeXB0ZWQ6IHRydWUsXG4gICAgICAgICAgY29udGVudEhhc2g6ICcweDlhYmMuLi4nLFxuICAgICAgICB9LFxuICAgICAgXTtcblxuICAgICAgc2V0TWVzc2FnZXMoc2ltdWxhdGVkTWVzc2FnZXMpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBtZXNzYWdlczonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGRlY3J5cHRNZXNzYWdlQ29udGVudCA9IGFzeW5jIChtZXNzYWdlSGFzaDogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgICAgIC8vIDEuIFJldHJpZXZlIHRoZSBlbmNyeXB0ZWQgY29udGVudCBmcm9tIG9mZi1jaGFpbiBzdG9yYWdlIHVzaW5nIHRoZSBoYXNoXG4gICAgICAvLyAyLiBEZWNyeXB0IGl0IHVzaW5nIHRoZSByZWNpcGllbnQncyBwcml2YXRlIGtleVxuICAgICAgY29uc3QgZGVjcnlwdGVkQ29udGVudCA9IGF3YWl0IGRlY3J5cHRNZXNzYWdlKG1lc3NhZ2VIYXNoLCBhY2NvdW50Py5nZXRBZGRyZXNzKCkudG9TdHJpbmcoKSB8fCAnJyk7XG4gICAgICByZXR1cm4gZGVjcnlwdGVkQ29udGVudDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGRlY3J5cHQgbWVzc2FnZTonLCBlcnJvcik7XG4gICAgICByZXR1cm4gJ0ZhaWxlZCB0byBkZWNyeXB0IG1lc3NhZ2UnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmb3JtYXRUaW1lID0gKHRpbWVzdGFtcDogRGF0ZSkgPT4ge1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgZGlmZiA9IG5vdy5nZXRUaW1lKCkgLSB0aW1lc3RhbXAuZ2V0VGltZSgpO1xuICAgIGNvbnN0IG1pbnV0ZXMgPSBNYXRoLmZsb29yKGRpZmYgLyA2MDAwMCk7XG4gICAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKGRpZmYgLyAzNjAwMDAwKTtcbiAgICBjb25zdCBkYXlzID0gTWF0aC5mbG9vcihkaWZmIC8gODY0MDAwMDApO1xuXG4gICAgaWYgKG1pbnV0ZXMgPCAxKSByZXR1cm4gJ0p1c3Qgbm93JztcbiAgICBpZiAobWludXRlcyA8IDYwKSByZXR1cm4gYCR7bWludXRlc31tIGFnb2A7XG4gICAgaWYgKGhvdXJzIDwgMjQpIHJldHVybiBgJHtob3Vyc31oIGFnb2A7XG4gICAgcmV0dXJuIGAke2RheXN9ZCBhZ29gO1xuICB9O1xuXG4gIGNvbnN0IGlzT3duTWVzc2FnZSA9IChzZW5kZXI6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBzZW5kZXIgPT09IGFjY291bnQ/LmdldEFkZHJlc3MoKS50b1N0cmluZygpO1xuICB9O1xuXG4gIGlmIChpc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gaC04IHctOCBteC1hdXRvIG1iLTJcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICA8Y2lyY2xlIGNsYXNzTmFtZT1cIm9wYWNpdHktMjVcIiBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiNFwiPjwvY2lyY2xlPlxuICAgICAgICAgICAgPHBhdGggY2xhc3NOYW1lPVwib3BhY2l0eS03NVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiBkPVwiTTQgMTJhOCA4IDAgMDE4LThWMEM1LjM3MyAwIDAgNS4zNzMgMCAxMmg0em0yIDUuMjkxQTcuOTYyIDcuOTYyIDAgMDE0IDEySDBjMCAzLjA0MiAxLjEzNSA1LjgyNCAzIDcuOTM4bDMtMi42NDd6XCI+PC9wYXRoPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIExvYWRpbmcgbWVzc2FnZXMuLi5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC00IHNwYWNlLXktNFwiPlxuICAgICAge21lc3NhZ2VzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LWdyYXktNDAwIG10LThcIj5cbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMTIgaC0xMiBteC1hdXRvIG1iLTQgb3BhY2l0eS01MFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTIwIDEzVjZhMiAyIDAgMDAtMi0ySDZhMiAyIDAgMDAtMiAydjdtMTYgMHY1YTIgMiAwIDAxLTIgMkg2YTIgMiAwIDAxLTItMnYtNW0xNiAwaC0yLjU4NmExIDEgMCAwMC0uNzA3LjI5M2wtMi40MTQgMi40MTRhMSAxIDAgMDEtLjcwNy4yOTNoLTMuMTcyYTEgMSAwIDAxLS43MDctLjI5M2wtMi40MTQtMi40MTRBMSAxIDAgMDA2LjU4NiAxM0g0XCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8cD5ObyBtZXNzYWdlcyB5ZXQ8L3A+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBtdC0xXCI+U3RhcnQgYSBjb252ZXJzYXRpb24hPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICkgOiAoXG4gICAgICAgIG1lc3NhZ2VzLm1hcCgobWVzc2FnZSkgPT4gKFxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGtleT17bWVzc2FnZS5pZH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggJHtpc093bk1lc3NhZ2UobWVzc2FnZS5zZW5kZXIpID8gJ2p1c3RpZnktZW5kJyA6ICdqdXN0aWZ5LXN0YXJ0J31gfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgbWF4LXcteHMgbGc6bWF4LXctbWQgcHgtNCBweS0yIHJvdW5kZWQtbGcgJHtcbiAgICAgICAgICAgICAgICBpc093bk1lc3NhZ2UobWVzc2FnZS5zZW5kZXIpXG4gICAgICAgICAgICAgICAgICA/ICdiZy1wdXJwbGUtNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICA6ICdiZy13aGl0ZS8xMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi0xXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBvcGFjaXR5LTc1XCI+XG4gICAgICAgICAgICAgICAgICB7aXNPd25NZXNzYWdlKG1lc3NhZ2Uuc2VuZGVyKSA/ICdZb3UnIDogJ0NvbnRhY3QnfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIG9wYWNpdHktNzUgbWwtMlwiPlxuICAgICAgICAgICAgICAgICAge2Zvcm1hdFRpbWUobWVzc2FnZS50aW1lc3RhbXApfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc21cIj57bWVzc2FnZS5jb250ZW50fTwvcD5cbiAgICAgICAgICAgICAge21lc3NhZ2UuaXNEZWNyeXB0ZWQgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMgaC0zIHRleHQtZ3JlZW4tNDAwIG1yLTFcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTEwIDE4YTggOCAwIDEwMC0xNiA4IDggMCAwMDAgMTZ6bTMuNzA3LTkuMjkzYTEgMSAwIDAwLTEuNDE0LTEuNDE0TDkgMTAuNTg2IDcuNzA3IDkuMjkzYTEgMSAwIDAwLTEuNDE0IDEuNDE0bDIgMmExIDEgMCAwMDEuNDE0IDBsNC00elwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgb3BhY2l0eS03NVwiPkRlY3J5cHRlZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApKVxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUF6dGVjIiwiZGVjcnlwdE1lc3NhZ2UiLCJNZXNzYWdlSW5ib3giLCJjb250YWN0QWRkcmVzcyIsImFjY291bnQiLCJweGUiLCJjb250cmFjdEFkZHJlc3MiLCJtZXNzYWdlcyIsInNldE1lc3NhZ2VzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwibG9hZE1lc3NhZ2VzIiwic2ltdWxhdGVkTWVzc2FnZXMiLCJpZCIsInNlbmRlciIsImNvbnRlbnQiLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwiaXNEZWNyeXB0ZWQiLCJjb250ZW50SGFzaCIsImdldEFkZHJlc3MiLCJ0b1N0cmluZyIsImVycm9yIiwiY29uc29sZSIsImRlY3J5cHRNZXNzYWdlQ29udGVudCIsIm1lc3NhZ2VIYXNoIiwiZGVjcnlwdGVkQ29udGVudCIsImZvcm1hdFRpbWUiLCJkaWZmIiwiZ2V0VGltZSIsIm1pbnV0ZXMiLCJNYXRoIiwiZmxvb3IiLCJob3VycyIsImRheXMiLCJpc093bk1lc3NhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJzdmciLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94IiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInBhdGgiLCJkIiwibGVuZ3RoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwicCIsIm1hcCIsIm1lc3NhZ2UiLCJzcGFuIiwiZmlsbFJ1bGUiLCJjbGlwUnVsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessageInbox.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessagingInterface.tsx":
/*!***********************************************!*\
  !*** ./src/components/MessagingInterface.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessagingInterface: () => (/* binding */ MessagingInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* harmony import */ var _MessageComposer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageComposer */ \"(ssr)/./src/components/MessageComposer.tsx\");\n/* harmony import */ var _MessageInbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MessageInbox */ \"(ssr)/./src/components/MessageInbox.tsx\");\n/* harmony import */ var _ContactList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ContactList */ \"(ssr)/./src/components/ContactList.tsx\");\n/* harmony import */ var _KeyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./KeyManager */ \"(ssr)/./src/components/KeyManager.tsx\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ui_PrivacyIndicator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/PrivacyIndicator */ \"(ssr)/./src/components/ui/PrivacyIndicator.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MessagingInterface auto */ \n\n\n\n\n\n\n\n\n\nfunction MessagingInterface() {\n    const { account, disconnect } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('messages');\n    const [selectedContact, setSelectedContact] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userAddress = account?.getAddress().toString() || '';\n    const userInitials = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.getInitials)(userAddress);\n    const avatarColor = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.generateAvatarColor)(userAddress);\n    const tabs = [\n        {\n            id: 'messages',\n            label: 'Messages',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            id: 'contacts',\n            label: 'Contacts',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            id: 'keys',\n            label: 'Privacy',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex flex-col max-w-7xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"glass-card m-4 mb-0 p-4 border-b-0 rounded-b-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-12 h-12 ${avatarColor} rounded-full flex items-center justify-center relative`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: userInitials\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 -right-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"status-online\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-white gradient-text\",\n                                            children: \"MessAge\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-neutral-400 text-sm font-mono\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatAddress)(userAddress)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_PrivacyIndicator__WEBPACK_IMPORTED_MODULE_8__.PrivacyIndicator, {\n                                                    status: \"encrypted\",\n                                                    size: \"sm\",\n                                                    showText: false\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                    className: \"md:hidden p-2 text-neutral-400 hover:text-white transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: disconnect,\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"Disconnect\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"glass-card mx-4 mt-0 p-1 rounded-t-none border-t-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex ${isMobileMenuOpen ? 'flex-col md:flex-row' : 'hidden md:flex'}`,\n                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setActiveTab(tab.id);\n                                setIsMobileMenuOpen(false);\n                            },\n                            className: `flex items-center gap-3 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${activeTab === tab.id ? 'bg-primary-600 text-white shadow-lg transform scale-105' : 'text-neutral-400 hover:text-white hover:bg-white/10'}`,\n                            children: [\n                                tab.icon,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === tab.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    activeTab === 'messages' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-80 lg:w-96 glass-card m-4 mr-0 md:mr-2 rounded-r-none md:rounded-r-xl border-r-0 md:border-r\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContactList__WEBPACK_IMPORTED_MODULE_5__.ContactList, {\n                                    onSelectContact: setSelectedContact,\n                                    selectedContact: selectedContact\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex flex-1 flex-col glass-card m-4 ml-2 rounded-l-none\",\n                                children: selectedContact ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInbox__WEBPACK_IMPORTED_MODULE_4__.MessageInbox, {\n                                            contactAddress: selectedContact\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageComposer__WEBPACK_IMPORTED_MODULE_3__.MessageComposer, {\n                                            recipientAddress: selectedContact\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-12 h-12 text-primary-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                children: \"Start a Conversation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-neutral-400 leading-relaxed\",\n                                                children: \"Select a contact from the sidebar to begin sending encrypted messages with zero-knowledge proofs.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'contacts' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 glass-card m-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContactList__WEBPACK_IMPORTED_MODULE_5__.ContactList, {\n                            onSelectContact: setSelectedContact,\n                            selectedContact: selectedContact,\n                            showManagement: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 glass-card m-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-white mb-2\",\n                                            children: \"Privacy & Security\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-neutral-400\",\n                                            children: \"Manage your encryption keys and privacy settings\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KeyManager__WEBPACK_IMPORTED_MODULE_6__.KeyManager, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessagingInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/WalletConnection.tsx":
/*!*********************************************!*\
  !*** ./src/components/WalletConnection.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletConnection: () => (/* binding */ WalletConnection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ WalletConnection auto */ \n\n\n\nfunction WalletConnection() {\n    const { connect } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleConnect = async ()=>{\n        setIsConnecting(true);\n        try {\n            await connect();\n        } catch (error) {\n            console.error('Connection failed:', error);\n        } finally{\n            setIsConnecting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-lg mx-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass-card p-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-20 h-20 mx-auto mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full animate-pulse-subtle\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-full bg-neutral-900 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-10 h-10 text-primary-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white mb-3\",\n                            children: \"Connect to Aztec\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-300 text-lg leading-relaxed\",\n                            children: [\n                                \"Connect your wallet to the Aztec network and start sending\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-primary-400 font-medium\",\n                                    children: \" private messages\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this),\n                                \" with zero-knowledge proofs.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 space-y-3 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 text-neutral-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-primary-500/20 rounded-full flex items-center justify-center flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-primary-400\",\n                                        children: \"1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Connect your Aztec-compatible wallet\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 text-neutral-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-primary-500/20 rounded-full flex items-center justify-center flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-primary-400\",\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Generate your encryption keys automatically\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 text-neutral-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-primary-500/20 rounded-full flex items-center justify-center flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-primary-400\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Start messaging with complete privacy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: handleConnect,\n                    disabled: isConnecting,\n                    isLoading: isConnecting,\n                    size: \"lg\",\n                    className: \"w-full\",\n                    leftIcon: !isConnecting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 15\n                    }, void 0),\n                    children: isConnecting ? 'Connecting to Aztec...' : 'Connect Wallet'\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 p-4 bg-neutral-800/50 rounded-lg border border-neutral-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-5 text-success-400 flex-shrink-0 mt-0.5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-300 font-medium mb-1\",\n                                        children: \"Your privacy is guaranteed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-neutral-400\",\n                                        children: \"All messages are encrypted locally before being sent. Your private keys never leave your device.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/WalletConnection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Button auto */ \n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, variant = 'primary', size = 'md', isLoading = false, leftIcon, rightIcon, children, disabled, ...props }, ref)=>{\n    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-900 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variants = {\n        primary: 'bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white focus:ring-primary-500 shadow-lg hover:shadow-xl',\n        secondary: 'bg-transparent hover:bg-white/10 active:bg-white/20 text-neutral-300 border border-neutral-600 hover:border-neutral-500 focus:ring-neutral-500',\n        ghost: 'bg-transparent hover:bg-white/5 active:bg-white/10 text-neutral-400 hover:text-neutral-300 focus:ring-neutral-500',\n        glass: 'glass-button text-white hover:text-white focus:ring-primary-500'\n    };\n    const sizes = {\n        sm: 'px-3 py-1.5 text-sm rounded-md gap-1.5',\n        md: 'px-4 py-2 text-base rounded-lg gap-2',\n        lg: 'px-6 py-3 text-lg rounded-xl gap-2.5'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variants[variant], sizes[size], isLoading && 'cursor-wait', className),\n        disabled: disabled || isLoading,\n        ref: ref,\n        ...props,\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/Button.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/Button.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"flex-shrink-0\",\n                    children: leftIcon\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/Button.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 26\n                }, undefined),\n                children,\n                rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"flex-shrink-0\",\n                    children: rightIcon\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/Button.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 27\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/Button.tsx\",\n        lineNumber: 42,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = 'Button';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/MessageStatus.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/MessageStatus.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageStatus: () => (/* binding */ MessageStatus),\n/* harmony export */   useMessageStatus: () => (/* binding */ useMessageStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageStatus,useMessageStatus auto */ \n\n\nconst statusConfig = {\n    sending: {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-3 h-3 border border-current border-t-transparent rounded-full animate-spin\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined),\n        color: 'text-neutral-400',\n        description: 'Sending message...'\n    },\n    sent: {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-3 h-3\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                clipRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined),\n        color: 'text-neutral-400',\n        description: 'Message sent'\n    },\n    delivered: {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-3 h-3\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-3 h-3 -ml-1\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined),\n        color: 'text-neutral-400',\n        description: 'Message delivered'\n    },\n    read: {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-3 h-3\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-3 h-3 -ml-1\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined),\n        color: 'text-primary-400',\n        description: 'Message read'\n    },\n    failed: {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-3 h-3\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                clipRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, undefined),\n        color: 'text-error-400',\n        description: 'Failed to send'\n    }\n};\nfunction formatTime(date) {\n    return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n    });\n}\nfunction MessageStatus({ status, timestamp, className, showTimestamp = true }) {\n    const config = statusConfig[status];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center gap-1 text-xs', config.color, className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex-shrink-0\",\n                title: config.description,\n                children: config.icon\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            showTimestamp && timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs\",\n                children: formatTime(timestamp)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/MessageStatus.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n// Hook for managing message status\nfunction useMessageStatus() {\n    const [status, setStatus] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('sending');\n    const [timestamp, setTimestamp] = react__WEBPACK_IMPORTED_MODULE_1___default().useState();\n    const updateStatus = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useMessageStatus.useCallback[updateStatus]\": (newStatus)=>{\n            setStatus(newStatus);\n            if (newStatus !== 'sending') {\n                setTimestamp(new Date());\n            }\n        }\n    }[\"useMessageStatus.useCallback[updateStatus]\"], []);\n    const sendMessage = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useMessageStatus.useCallback[sendMessage]\": async ()=>{\n            setStatus('sending');\n            setTimestamp(undefined);\n            try {\n                // Simulate message sending\n                await new Promise({\n                    \"useMessageStatus.useCallback[sendMessage]\": (resolve)=>setTimeout(resolve, 1000)\n                }[\"useMessageStatus.useCallback[sendMessage]\"]);\n                updateStatus('sent');\n                // Simulate delivery\n                setTimeout({\n                    \"useMessageStatus.useCallback[sendMessage]\": ()=>updateStatus('delivered')\n                }[\"useMessageStatus.useCallback[sendMessage]\"], 2000);\n                // Simulate read (optional)\n                setTimeout({\n                    \"useMessageStatus.useCallback[sendMessage]\": ()=>updateStatus('read')\n                }[\"useMessageStatus.useCallback[sendMessage]\"], 5000);\n            } catch (error) {\n                updateStatus('failed');\n            }\n        }\n    }[\"useMessageStatus.useCallback[sendMessage]\"], [\n        updateStatus\n    ]);\n    return {\n        status,\n        timestamp,\n        updateStatus,\n        sendMessage\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/MessageStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/PrivacyIndicator.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/PrivacyIndicator.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PrivacyIndicator: () => (/* binding */ PrivacyIndicator),\n/* harmony export */   usePrivacyStatus: () => (/* binding */ usePrivacyStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ PrivacyIndicator,usePrivacyStatus auto */ \n\n\nconst statusConfig = {\n    encrypted: {\n        icon: '🔒',\n        text: 'Encrypted',\n        description: 'Message is encrypted with zero-knowledge proof',\n        className: 'privacy-encrypted',\n        bgColor: 'bg-success-600',\n        textColor: 'text-white'\n    },\n    unencrypted: {\n        icon: '⚠️',\n        text: 'Unencrypted',\n        description: 'Message is not encrypted',\n        className: 'privacy-unencrypted',\n        bgColor: 'bg-warning-600',\n        textColor: 'text-white'\n    },\n    verifying: {\n        icon: '⏳',\n        text: 'Verifying',\n        description: 'Generating zero-knowledge proof...',\n        className: 'privacy-verifying',\n        bgColor: 'bg-neutral-600',\n        textColor: 'text-white'\n    },\n    error: {\n        icon: '❌',\n        text: 'Error',\n        description: 'Failed to encrypt message',\n        className: 'privacy-error',\n        bgColor: 'bg-error-600',\n        textColor: 'text-white'\n    }\n};\nconst sizeConfig = {\n    sm: {\n        container: 'px-2 py-1 text-xs gap-1',\n        icon: 'text-xs',\n        text: 'text-xs'\n    },\n    md: {\n        container: 'px-3 py-1.5 text-sm gap-1.5',\n        icon: 'text-sm',\n        text: 'text-sm'\n    },\n    lg: {\n        container: 'px-4 py-2 text-base gap-2',\n        icon: 'text-base',\n        text: 'text-base'\n    }\n};\nfunction PrivacyIndicator({ status, className, showText = true, size = 'md', onClick }) {\n    const config = statusConfig[status];\n    const sizeStyles = sizeConfig[size];\n    const isClickable = !!onClick;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('inline-flex items-center rounded-full font-medium transition-all duration-200', config.bgColor, config.textColor, sizeStyles.container, isClickable && 'cursor-pointer hover:opacity-80 active:scale-95', className),\n        onClick: onClick,\n        title: config.description,\n        role: isClickable ? 'button' : undefined,\n        tabIndex: isClickable ? 0 : undefined,\n        onKeyDown: isClickable ? (e)=>{\n            if (e.key === 'Enter' || e.key === ' ') {\n                e.preventDefault();\n                onClick?.();\n            }\n        } : undefined,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex-shrink-0', sizeStyles.icon),\n                children: status === 'verifying' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-3 h-3 border border-current border-t-transparent rounded-full animate-spin\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/PrivacyIndicator.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this) : config.icon\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/PrivacyIndicator.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('font-medium', sizeStyles.text),\n                children: config.text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/PrivacyIndicator.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/PrivacyIndicator.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n// Hook for managing privacy status\nfunction usePrivacyStatus() {\n    const [status, setStatus] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('unencrypted');\n    const [isGeneratingProof, setIsGeneratingProof] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const generateProof = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"usePrivacyStatus.useCallback[generateProof]\": async ()=>{\n            setIsGeneratingProof(true);\n            setStatus('verifying');\n            try {\n                // Simulate ZK proof generation\n                await new Promise({\n                    \"usePrivacyStatus.useCallback[generateProof]\": (resolve)=>setTimeout(resolve, 2000)\n                }[\"usePrivacyStatus.useCallback[generateProof]\"]);\n                setStatus('encrypted');\n            } catch (error) {\n                console.error('Failed to generate proof:', error);\n                setStatus('error');\n            } finally{\n                setIsGeneratingProof(false);\n            }\n        }\n    }[\"usePrivacyStatus.useCallback[generateProof]\"], []);\n    const resetStatus = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"usePrivacyStatus.useCallback[resetStatus]\": ()=>{\n            setStatus('unencrypted');\n            setIsGeneratingProof(false);\n        }\n    }[\"usePrivacyStatus.useCallback[resetStatus]\"], []);\n    return {\n        status,\n        isGeneratingProof,\n        generateProof,\n        resetStatus,\n        setStatus\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/PrivacyIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/TypingIndicator.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/TypingIndicator.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TypingIndicator: () => (/* binding */ TypingIndicator),\n/* harmony export */   useTypingIndicator: () => (/* binding */ useTypingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ TypingIndicator,useTypingIndicator auto */ \n\n\nfunction TypingIndicator({ isVisible, userName, className }) {\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center gap-2 px-4 py-2 text-neutral-400 text-sm animate-fade-in', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-neutral-500 rounded-full animate-bounce\",\n                            style: {\n                                animationDelay: '0ms'\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/TypingIndicator.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-neutral-500 rounded-full animate-bounce\",\n                            style: {\n                                animationDelay: '150ms'\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/TypingIndicator.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-neutral-500 rounded-full animate-bounce\",\n                            style: {\n                                animationDelay: '300ms'\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/TypingIndicator.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/TypingIndicator.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/TypingIndicator.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: userName ? `${userName} is typing...` : 'Someone is typing...'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/TypingIndicator.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ui/TypingIndicator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n// Hook for managing typing state\nfunction useTypingIndicator(timeout = 3000) {\n    const [isTyping, setIsTyping] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const timeoutRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const startTyping = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useTypingIndicator.useCallback[startTyping]\": ()=>{\n            setIsTyping(true);\n            // Clear existing timeout\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            // Set new timeout\n            timeoutRef.current = setTimeout({\n                \"useTypingIndicator.useCallback[startTyping]\": ()=>{\n                    setIsTyping(false);\n                }\n            }[\"useTypingIndicator.useCallback[startTyping]\"], timeout);\n        }\n    }[\"useTypingIndicator.useCallback[startTyping]\"], [\n        timeout\n    ]);\n    const stopTyping = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useTypingIndicator.useCallback[stopTyping]\": ()=>{\n            setIsTyping(false);\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n        }\n    }[\"useTypingIndicator.useCallback[stopTyping]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"useTypingIndicator.useEffect\": ()=>{\n            return ({\n                \"useTypingIndicator.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"useTypingIndicator.useEffect\"];\n        }\n    }[\"useTypingIndicator.useEffect\"], []);\n    return {\n        isTyping,\n        startTyping,\n        stopTyping\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/TypingIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/aztec-context.tsx":
/*!***********************************!*\
  !*** ./src/lib/aztec-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AztecProvider: () => (/* binding */ AztecProvider),\n/* harmony export */   useAztec: () => (/* binding */ useAztec)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AztecProvider,useAztec auto */ \n\nconst AztecContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AztecProvider({ children }) {\n    const [pxe, setPxe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contractAddress, setContractAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const connect = async ()=>{\n        try {\n            // Simulate connection to Aztec sandbox\n            console.log('Connecting to Aztec sandbox...');\n            // Simulate delay\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Create mock PXE client\n            const mockPxe = {\n                url: 'http://localhost:8080',\n                isConnected: true\n            };\n            setPxe(mockPxe);\n            // Create mock account\n            const mockAccount = {\n                getAddress: ()=>({\n                        toString: ()=>'0x' + Math.random().toString(16).slice(2, 42).padStart(40, '0')\n                    })\n            };\n            setAccount(mockAccount);\n            setIsConnected(true);\n            // Set mock contract address\n            setContractAddress('0x' + Math.random().toString(16).slice(2, 42).padStart(40, '0'));\n            console.log('Connected to Aztec network (simulated)');\n        } catch (error) {\n            console.error('Failed to connect to Aztec:', error);\n            alert('Failed to connect to Aztec network. This is a demo version.');\n        }\n    };\n    const disconnect = ()=>{\n        setPxe(null);\n        setAccount(null);\n        setIsConnected(false);\n        setContractAddress(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AztecContext.Provider, {\n        value: {\n            pxe,\n            account,\n            isConnected,\n            connect,\n            disconnect,\n            contractAddress\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/lib/aztec-context.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\nfunction useAztec() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AztecContext);\n    if (context === undefined) {\n        throw new Error('useAztec must be used within an AztecProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/aztec-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/crypto.ts":
/*!***************************!*\
  !*** ./src/lib/crypto.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decryptFromStorage: () => (/* binding */ decryptFromStorage),\n/* harmony export */   decryptMessage: () => (/* binding */ decryptMessage),\n/* harmony export */   deriveSharedSecret: () => (/* binding */ deriveSharedSecret),\n/* harmony export */   encryptForStorage: () => (/* binding */ encryptForStorage),\n/* harmony export */   encryptMessage: () => (/* binding */ encryptMessage),\n/* harmony export */   exportPublicKey: () => (/* binding */ exportPublicKey),\n/* harmony export */   generateKeyPair: () => (/* binding */ generateKeyPair),\n/* harmony export */   generateKeyPairFromSeed: () => (/* binding */ generateKeyPairFromSeed),\n/* harmony export */   generateMessageProof: () => (/* binding */ generateMessageProof),\n/* harmony export */   generateNonce: () => (/* binding */ generateNonce),\n/* harmony export */   hashMessage: () => (/* binding */ hashMessage),\n/* harmony export */   isValidAztecAddress: () => (/* binding */ isValidAztecAddress),\n/* harmony export */   signMessage: () => (/* binding */ signMessage),\n/* harmony export */   verifyMessageProof: () => (/* binding */ verifyMessageProof),\n/* harmony export */   verifySignature: () => (/* binding */ verifySignature)\n/* harmony export */ });\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto-js */ \"(ssr)/./node_modules/crypto-js/index.js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Generate a new key pair for encryption\nasync function generateKeyPair() {\n    // In a real implementation, this would use proper elliptic curve cryptography\n    // For demo purposes, we'll use a simplified approach\n    const privateKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().lib.WordArray.random(32).toString();\n    const publicKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(privateKey).toString();\n    return {\n        publicKey: `0x${publicKey}`,\n        privateKey: `0x${privateKey}`\n    };\n}\n// Export public key in a shareable format\nfunction exportPublicKey(publicKey) {\n    return publicKey;\n}\n// Encrypt a message using the recipient's public key\nasync function encryptMessage(message, recipientPublicKey) {\n    try {\n        // In a real implementation, this would use ECIES (Elliptic Curve Integrated Encryption Scheme)\n        // For demo purposes, we'll use AES encryption with a derived key\n        const derivedKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(recipientPublicKey).toString();\n        const encrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.encrypt(message, derivedKey).toString();\n        return encrypted;\n    } catch (error) {\n        console.error('Encryption failed:', error);\n        throw new Error('Failed to encrypt message');\n    }\n}\n// Decrypt a message using the recipient's private key\nasync function decryptMessage(encryptedMessage, privateKey) {\n    try {\n        // In a real implementation, this would use the private key to decrypt ECIES-encrypted data\n        // For demo purposes, we'll use the private key to derive the AES key\n        const derivedKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(privateKey).toString();\n        const decrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encryptedMessage, derivedKey);\n        const decryptedText = decrypted.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n        if (!decryptedText) {\n            throw new Error('Decryption failed - invalid key or corrupted data');\n        }\n        return decryptedText;\n    } catch (error) {\n        console.error('Decryption failed:', error);\n        throw new Error('Failed to decrypt message');\n    }\n}\n// Create a hash of the message content\nfunction hashMessage(content) {\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(content).toString();\n}\n// Generate a zero-knowledge proof for message ownership\nasync function generateMessageProof(message, privateKey, recipientPublicKey) {\n    // In a real implementation, this would generate a ZK-SNARK proof\n    // proving that the sender knows the message content without revealing it\n    const messageHash = hashMessage(message);\n    const commitment = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(messageHash + privateKey).toString();\n    // Simplified proof structure\n    const proof = {\n        proof: `0x${commitment}`,\n        publicSignals: [\n            messageHash,\n            crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(privateKey).toString(),\n            recipientPublicKey\n        ]\n    };\n    return proof;\n}\n// Verify a zero-knowledge proof\nasync function verifyMessageProof(proof, expectedMessageHash) {\n    try {\n        // In a real implementation, this would verify a ZK-SNARK proof\n        // For demo purposes, we'll do a simple verification\n        const [messageHash, senderPublicKey, recipientPublicKey] = proof.publicSignals;\n        // Verify that the message hash matches\n        if (messageHash !== expectedMessageHash) {\n            return false;\n        }\n        // Verify proof structure (simplified)\n        const expectedCommitment = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(messageHash + 'dummy_private_key').toString();\n        return proof.proof.includes(messageHash.slice(0, 8));\n    } catch (error) {\n        console.error('Proof verification failed:', error);\n        return false;\n    }\n}\n// Generate a secure random nonce\nfunction generateNonce() {\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().lib.WordArray.random(16).toString();\n}\n// Derive a shared secret between two parties (simplified ECDH)\nasync function deriveSharedSecret(privateKey, publicKey) {\n    // In a real implementation, this would use ECDH key agreement\n    // For demo purposes, we'll use a simple hash combination\n    const combined = privateKey + publicKey;\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(combined).toString();\n}\n// Encrypt data for storage (using a password)\nfunction encryptForStorage(data, password) {\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.encrypt(data, password).toString();\n}\n// Decrypt data from storage\nfunction decryptFromStorage(encryptedData, password) {\n    const decrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encryptedData, password);\n    return decrypted.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n}\n// Validate an Aztec address format\nfunction isValidAztecAddress(address) {\n    // Basic validation for Aztec address format\n    return /^0x[a-fA-F0-9]{40}$/.test(address);\n}\n// Generate a deterministic key pair from a seed\nasync function generateKeyPairFromSeed(seed) {\n    const privateKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(seed).toString();\n    const publicKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(privateKey + 'public').toString();\n    return {\n        publicKey: `0x${publicKey}`,\n        privateKey: `0x${privateKey}`\n    };\n}\n// Create a message signature for authentication\nfunction signMessage(message, privateKey) {\n    const messageHash = hashMessage(message);\n    const signature = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().HmacSHA256(messageHash, privateKey).toString();\n    return `0x${signature}`;\n}\n// Verify a message signature\nfunction verifySignature(message, signature, publicKey) {\n    try {\n        const messageHash = hashMessage(message);\n        // In a real implementation, this would verify an ECDSA signature\n        // For demo purposes, we'll use a simplified verification\n        return signature.length === 66 && signature.startsWith('0x');\n    } catch (error) {\n        console.error('Signature verification failed:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/crypto.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatAddress: () => (/* binding */ formatAddress),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatMessageTime: () => (/* binding */ formatMessageTime),\n/* harmony export */   generateAvatarColor: () => (/* binding */ generateAvatarColor),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getRelativeTime: () => (/* binding */ getRelativeTime),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isValidAztecAddress: () => (/* binding */ isValidAztecAddress),\n/* harmony export */   sanitizeString: () => (/* binding */ sanitizeString),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n * Combines clsx for conditional classes and tailwind-merge for deduplication\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format timestamp for message display\n */ function formatMessageTime(timestamp) {\n    const now = new Date();\n    const diff = now.getTime() - timestamp.getTime();\n    const minutes = Math.floor(diff / (1000 * 60));\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    if (minutes < 1) return 'now';\n    if (minutes < 60) return `${minutes}m`;\n    if (hours < 24) return `${hours}h`;\n    if (days < 7) return `${days}d`;\n    return timestamp.toLocaleDateString();\n}\n/**\n * Truncate text with ellipsis\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\n/**\n * Generate a random color for avatars\n */ function generateAvatarColor(seed) {\n    const colors = [\n        'bg-red-500',\n        'bg-blue-500',\n        'bg-green-500',\n        'bg-yellow-500',\n        'bg-purple-500',\n        'bg-pink-500',\n        'bg-indigo-500',\n        'bg-teal-500'\n    ];\n    let hash = 0;\n    for(let i = 0; i < seed.length; i++){\n        hash = seed.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    return colors[Math.abs(hash) % colors.length];\n}\n/**\n * Debounce function for search and input handling\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (error) {\n        console.error('Failed to copy to clipboard:', error);\n        return false;\n    }\n}\n/**\n * Format file size for display\n */ function formatFileSize(bytes) {\n    const sizes = [\n        'B',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    if (bytes === 0) return '0 B';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;\n}\n/**\n * Validate Aztec address format\n */ function isValidAztecAddress(address) {\n    return /^0x[a-fA-F0-9]{40}$/.test(address);\n}\n/**\n * Generate initials from name\n */ function getInitials(name) {\n    return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n}\n/**\n * Sleep utility for async operations\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Check if device is mobile\n */ function isMobile() {\n    return  false && 0;\n}\n/**\n * Format address for display (truncated)\n */ function formatAddress(address, startChars = 6, endChars = 4) {\n    if (address.length <= startChars + endChars) return address;\n    return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;\n}\n/**\n * Generate a unique ID\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n/**\n * Check if string is empty or whitespace\n */ function isEmpty(str) {\n    return !str || str.trim().length === 0;\n}\n/**\n * Sanitize string for display\n */ function sanitizeString(str) {\n    return str.replace(/[<>]/g, '');\n}\n/**\n * Get relative time string\n */ function getRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;\n    return date.toLocaleDateString();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/crypto-js","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();