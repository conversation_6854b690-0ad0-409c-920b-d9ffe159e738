/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Developer/mess-age/frontend/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPSUyRlVzZXJzJTJGYWJkdWxsYWhpYWJkaSUyRkRldmVsb3BlciUyRm1lc3MtYWdlJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGYWJkdWxsYWhpYWJkaSUyRkRldmVsb3BlciUyRm1lc3MtYWdlJTJGZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQixvSkFBd0c7QUFDOUgsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsb0JBQW9CLGdKQUFzRztBQUd4SDtBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQWdRO0FBQ3BTO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBZ1E7QUFDcFM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWJkdWxsYWhpYWJkaS9EZXZlbG9wZXIvbWVzcy1hZ2UvZnJvbnRlbmQvc3JjL2FwcC9sYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgcGFnZTQgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hYmR1bGxhaGlhYmRpL0RldmVsb3Blci9tZXNzLWFnZS9mcm9udGVuZC9zcmMvYXBwL3BhZ2UudHN4XCIpO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zc3InXG59O1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbcGFnZTQsIFwiL1VzZXJzL2FiZHVsbGFoaWFiZGkvRGV2ZWxvcGVyL21lc3MtYWdlL2Zyb250ZW5kL3NyYy9hcHAvcGFnZS50c3hcIl0sXG4gICAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS9Vc2Vycy9hYmR1bGxhaGlhYmRpL0RldmVsb3Blci9tZXNzLWFnZS9mcm9udGVuZC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiL1VzZXJzL2FiZHVsbGFoaWFiZGkvRGV2ZWxvcGVyL21lc3MtYWdlL2Zyb250ZW5kL3NyYy9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS9Vc2Vycy9hYmR1bGxhaGlhYmRpL0RldmVsb3Blci9tZXNzLWFnZS9mcm9udGVuZC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiL1VzZXJzL2FiZHVsbGFoaWFiZGkvRGV2ZWxvcGVyL21lc3MtYWdlL2Zyb250ZW5kL3NyYy9hcHAvcGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWJkdWxsYWhpYWJkaSUyRkRldmVsb3BlciUyRm1lc3MtYWdlJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWJkdWxsYWhpYWJkaS9EZXZlbG9wZXIvbWVzcy1hZ2UvZnJvbnRlbmQvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYmR1bGxhaGlhYmRpL0RldmVsb3Blci9tZXNzLWFnZS9mcm9udGVuZC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWJkdWxsYWhpYWJkaS9EZXZlbG9wZXIvbWVzcy1hZ2UvZnJvbnRlbmQvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYmR1bGxhaGlhYmRpL0RldmVsb3Blci9tZXNzLWFnZS9mcm9udGVuZC9zcmMvYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWJkdWxsYWhpYWJkaSUyRkRldmVsb3BlciUyRm1lc3MtYWdlJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWJkdWxsYWhpYWJkaS9EZXZlbG9wZXIvbWVzcy1hZ2UvZnJvbnRlbmQvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_MessagingInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/MessagingInterface */ \"(ssr)/./src/components/MessagingInterface.tsx\");\n/* harmony import */ var _components_WalletConnection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/WalletConnection */ \"(ssr)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_aztec_context__WEBPACK_IMPORTED_MODULE_4__.AztecProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-white mb-2\",\n                                children: \"MessAge\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg\",\n                                children: \"Zero-Knowledge Private Messaging on Aztec\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mt-2\",\n                                children: \"Your messages are encrypted client-side. The blockchain never sees your content.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this),\n                    !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WalletConnection__WEBPACK_IMPORTED_MODULE_3__.WalletConnection, {\n                        onConnect: ()=>setIsConnected(true)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessagingInterface__WEBPACK_IMPORTED_MODULE_2__.MessagingInterface, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/app/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ContactList.tsx":
/*!****************************************!*\
  !*** ./src/components/ContactList.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactList: () => (/* binding */ ContactList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContactList auto */ \n\n\nfunction ContactList({ onSelectContact, selectedContact, showManagement = false }) {\n    const { account, pxe } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [contacts, setContacts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddContact, setShowAddContact] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newContactAddress, setNewContactAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newContactName, setNewContactName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactList.useEffect\": ()=>{\n            loadContacts();\n        }\n    }[\"ContactList.useEffect\"], [\n        account\n    ]);\n    const loadContacts = async ()=>{\n        if (!account) return;\n        setIsLoading(true);\n        try {\n            // In a real implementation, this would load contacts from local storage\n            // and verify their public keys from the contract\n            const simulatedContacts = [\n                {\n                    address: '0x1234567890abcdef1234567890abcdef12345678',\n                    name: 'Alice',\n                    publicKey: '0xabc123...',\n                    lastMessage: 'Hello! This is a private message.',\n                    lastMessageTime: new Date(Date.now() - 3600000),\n                    unreadCount: 2\n                },\n                {\n                    address: '0xfedcba0987654321fedcba0987654321fedcba09',\n                    name: 'Bob',\n                    publicKey: '0xdef456...',\n                    lastMessage: 'Thanks for the update!',\n                    lastMessageTime: new Date(Date.now() - 7200000),\n                    unreadCount: 0\n                },\n                {\n                    address: '0x1111222233334444555566667777888899990000',\n                    name: 'Charlie',\n                    publicKey: '0x789abc...',\n                    lastMessage: 'See you tomorrow',\n                    lastMessageTime: new Date(Date.now() - ********),\n                    unreadCount: 1\n                }\n            ];\n            setContacts(simulatedContacts);\n        } catch (error) {\n            console.error('Failed to load contacts:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const addContact = async ()=>{\n        if (!newContactAddress.trim() || !newContactName.trim()) return;\n        try {\n            // In a real implementation, this would:\n            // 1. Validate the address format\n            // 2. Check if the address has a registered public key on the contract\n            // 3. Add to local storage\n            const newContact = {\n                address: newContactAddress,\n                name: newContactName,\n                publicKey: '0x' + Math.random().toString(16).slice(2, 10) + '...',\n                unreadCount: 0\n            };\n            setContacts([\n                ...contacts,\n                newContact\n            ]);\n            setNewContactAddress('');\n            setNewContactName('');\n            setShowAddContact(false);\n        } catch (error) {\n            console.error('Failed to add contact:', error);\n            alert('Failed to add contact. Please check the address.');\n        }\n    };\n    const removeContact = (address)=>{\n        if (confirm('Are you sure you want to remove this contact?')) {\n            setContacts(contacts.filter((c)=>c.address !== address));\n            if (selectedContact === address) {\n                onSelectContact('');\n            }\n        }\n    };\n    const formatTime = (timestamp)=>{\n        if (!timestamp) return '';\n        const now = new Date();\n        const diff = now.getTime() - timestamp.getTime();\n        const minutes = Math.floor(diff / 60000);\n        const hours = Math.floor(diff / 3600000);\n        const days = Math.floor(diff / ********);\n        if (minutes < 1) return 'now';\n        if (minutes < 60) return `${minutes}m`;\n        if (hours < 24) return `${hours}h`;\n        return `${days}d`;\n    };\n    const truncateAddress = (address)=>{\n        return `${address.slice(0, 6)}...${address.slice(-4)}`;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 text-center text-gray-400\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-spin h-6 w-6 mx-auto mb-2\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                \"Loading contacts...\"\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-white font-semibold\",\n                            children: \"Contacts\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        showManagement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowAddContact(true),\n                            className: \"text-purple-400 hover:text-purple-300 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            showAddContact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-white/10 bg-white/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: \"Contact name\",\n                            value: newContactName,\n                            onChange: (e)=>setNewContactName(e.target.value),\n                            className: \"w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: \"Aztec address (0x...)\",\n                            value: newContactAddress,\n                            onChange: (e)=>setNewContactAddress(e.target.value),\n                            className: \"w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: addContact,\n                                    className: \"flex-1 bg-purple-600 hover:bg-purple-700 text-white text-sm py-2 px-3 rounded transition-colors\",\n                                    children: \"Add\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddContact(false),\n                                    className: \"flex-1 bg-gray-600 hover:bg-gray-700 text-white text-sm py-2 px-3 rounded transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: contacts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 text-center text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-12 h-12 mx-auto mb-4 opacity-50\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No contacts yet\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm mt-1\",\n                            children: \"Add contacts to start messaging\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this) : contacts.map((contact)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>onSelectContact(contact.address),\n                        className: `p-4 border-b border-white/10 cursor-pointer transition-colors hover:bg-white/5 ${selectedContact === contact.address ? 'bg-white/10' : ''}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-sm\",\n                                                children: contact.name.slice(0, 2).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-white font-medium truncate\",\n                                                            children: contact.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        contact.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400 ml-2\",\n                                                            children: formatTime(contact.lastMessageTime)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm truncate\",\n                                                    children: truncateAddress(contact.address)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this),\n                                                contact.lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs truncate mt-1\",\n                                                    children: contact.lastMessage\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        contact.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-purple-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\",\n                                            children: contact.unreadCount\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 21\n                                        }, this),\n                                        showManagement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                removeContact(contact.address);\n                                            },\n                                            className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 15\n                        }, this)\n                    }, contact.address, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/ContactList.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ContactList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/KeyManager.tsx":
/*!***************************************!*\
  !*** ./src/components/KeyManager.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeyManager: () => (/* binding */ KeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* harmony import */ var _lib_crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/crypto */ \"(ssr)/./src/lib/crypto.ts\");\n/* __next_internal_client_entry_do_not_use__ KeyManager auto */ \n\n\n\nfunction KeyManager() {\n    const { account, pxe, contractAddress } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [keyPair, setKeyPair] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRegistering, setIsRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPrivateKey, setShowPrivateKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"KeyManager.useEffect\": ()=>{\n            loadKeyPair();\n        }\n    }[\"KeyManager.useEffect\"], [\n        account\n    ]);\n    const loadKeyPair = async ()=>{\n        if (!account) return;\n        try {\n            // In a real implementation, this would load from secure storage\n            // For demo, we'll generate a key pair\n            const keys = await (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.generateKeyPair)();\n            setKeyPair({\n                publicKey: keys.publicKey,\n                privateKey: keys.privateKey,\n                isRegistered: false\n            });\n        } catch (error) {\n            console.error('Failed to load key pair:', error);\n        }\n    };\n    const generateNewKeyPair = async ()=>{\n        setIsGenerating(true);\n        try {\n            const keys = await (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.generateKeyPair)();\n            setKeyPair({\n                publicKey: keys.publicKey,\n                privateKey: keys.privateKey,\n                isRegistered: false\n            });\n        } catch (error) {\n            console.error('Failed to generate key pair:', error);\n            alert('Failed to generate key pair');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const registerPublicKey = async ()=>{\n        if (!keyPair || !account || !contractAddress) return;\n        setIsRegistering(true);\n        try {\n            // In a real implementation, this would call the contract's register_public_key function\n            console.log('Registering public key:', keyPair.publicKey);\n            // Simulate contract call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            setKeyPair({\n                ...keyPair,\n                isRegistered: true\n            });\n            alert('Public key registered successfully!');\n        } catch (error) {\n            console.error('Failed to register public key:', error);\n            alert('Failed to register public key');\n        } finally{\n            setIsRegistering(false);\n        }\n    };\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n        alert('Copied to clipboard!');\n    };\n    const truncateKey = (key, length = 20)=>{\n        return `${key.slice(0, length)}...${key.slice(-length)}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-2\",\n                        children: \"Key Management\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Manage your encryption keys for private messaging\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            keyPair ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 rounded-lg p-6 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white\",\n                                children: \"Your Key Pair\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: keyPair.isRegistered ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                    children: \"Registered\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-yellow-500 text-black text-xs px-2 py-1 rounded-full\",\n                                    children: \"Not Registered\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300\",\n                                children: \"Public Key (Share this with contacts)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: truncateKey(keyPair.publicKey),\n                                        readOnly: true,\n                                        className: \"flex-1 bg-white/10 border border-white/20 rounded px-3 py-2 text-white text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>copyToClipboard(keyPair.publicKey),\n                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-sm transition-colors\",\n                                        children: \"Copy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300\",\n                                children: \"Private Key (Keep this secret!)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: showPrivateKey ? 'text' : 'password',\n                                        value: showPrivateKey ? truncateKey(keyPair.privateKey) : '••••••••••••••••••••',\n                                        readOnly: true,\n                                        className: \"flex-1 bg-white/10 border border-white/20 rounded px-3 py-2 text-white text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPrivateKey(!showPrivateKey),\n                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm transition-colors\",\n                                        children: showPrivateKey ? 'Hide' : 'Show'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>copyToClipboard(keyPair.privateKey),\n                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-sm transition-colors\",\n                                        children: \"Copy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3 pt-4\",\n                        children: [\n                            !keyPair.isRegistered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: registerPublicKey,\n                                disabled: isRegistering,\n                                className: \"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded transition-colors flex items-center\",\n                                children: isRegistering ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Registering...\"\n                                    ]\n                                }, void 0, true) : 'Register Public Key'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: generateNewKeyPair,\n                                disabled: isGenerating,\n                                className: \"bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 text-white px-4 py-2 rounded transition-colors flex items-center\",\n                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Generating...\"\n                                    ]\n                                }, void 0, true) : 'Generate New Keys'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"No Key Pair Found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 mb-4\",\n                        children: \"Generate a new key pair to start messaging\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: generateNewKeyPair,\n                        disabled: isGenerating,\n                        className: \"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-6 py-3 rounded-lg transition-colors flex items-center mx-auto\",\n                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-2 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generating...\"\n                            ]\n                        }, void 0, true) : 'Generate Key Pair'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 text-yellow-500 mt-0.5\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-500 font-semibold text-sm\",\n                                    children: \"Security Notice\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mt-1\",\n                                    children: \"Your private key is used to decrypt messages. Never share it with anyone. Store it securely and back it up safely.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/KeyManager.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/KeyManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessageComposer.tsx":
/*!********************************************!*\
  !*** ./src/components/MessageComposer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageComposer: () => (/* binding */ MessageComposer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* harmony import */ var _lib_crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/crypto */ \"(ssr)/./src/lib/crypto.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageComposer auto */ \n\n\n\nfunction MessageComposer({ recipientAddress }) {\n    const { account, pxe, contractAddress } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSending, setIsSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sendMessage = async ()=>{\n        if (!message.trim() || !account || !pxe || !contractAddress) return;\n        setIsSending(true);\n        try {\n            // 1. Encrypt the message client-side\n            const encryptedContent = await (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.encryptMessage)(message, recipientAddress);\n            // 2. Create hash of the encrypted content\n            const contentHash = (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.hashMessage)(encryptedContent);\n            // 3. Send only the hash to the contract (not the actual message)\n            // This would call the contract's send_message function\n            // For now, we'll simulate it\n            console.log('Sending message hash to contract:', {\n                recipient: recipientAddress,\n                contentHash: contentHash.toString(),\n                originalMessage: message\n            });\n            // Simulate contract call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            setMessage('');\n            alert('Message sent successfully! Only the hash was stored on-chain.');\n        } catch (error) {\n            console.error('Failed to send message:', error);\n            alert('Failed to send message. Please try again.');\n        } finally{\n            setIsSending(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-t border-white/10 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: message,\n                            onChange: (e)=>setMessage(e.target.value),\n                            onKeyDown: handleKeyDown,\n                            placeholder: \"Type your private message...\",\n                            className: \"w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                            rows: 3,\n                            disabled: isSending\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mt-2 text-xs text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"\\uD83D\\uDD12 Message will be encrypted client-side before sending\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        message.length,\n                                        \"/1000\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: sendMessage,\n                    disabled: !message.trim() || isSending,\n                    className: \"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center\",\n                    children: isSending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        className: \"opacity-25\",\n                                        cx: \"12\",\n                                        cy: \"12\",\n                                        r: \"10\",\n                                        stroke: \"currentColor\",\n                                        strokeWidth: \"4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        className: \"opacity-75\",\n                                        fill: \"currentColor\",\n                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, this),\n                            \"Sending...\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 mr-2\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this),\n                            \"Send\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageComposer.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessageComposer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessageInbox.tsx":
/*!*****************************************!*\
  !*** ./src/components/MessageInbox.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageInbox: () => (/* binding */ MessageInbox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* harmony import */ var _lib_crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/crypto */ \"(ssr)/./src/lib/crypto.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageInbox auto */ \n\n\n\nfunction MessageInbox({ contactAddress }) {\n    const { account, pxe, contractAddress } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageInbox.useEffect\": ()=>{\n            loadMessages();\n        }\n    }[\"MessageInbox.useEffect\"], [\n        contactAddress,\n        account\n    ]);\n    const loadMessages = async ()=>{\n        if (!account || !pxe || !contractAddress) return;\n        setIsLoading(true);\n        try {\n            // In a real implementation, this would call the contract's get_messages function\n            // For now, we'll simulate some messages\n            const simulatedMessages = [\n                {\n                    id: '1',\n                    sender: contactAddress,\n                    content: 'Hello! This is a private message.',\n                    timestamp: new Date(Date.now() - 3600000),\n                    isDecrypted: true,\n                    contentHash: '0x1234...'\n                },\n                {\n                    id: '2',\n                    sender: account.getAddress().toString(),\n                    content: 'Hi there! Messages are encrypted end-to-end.',\n                    timestamp: new Date(Date.now() - 1800000),\n                    isDecrypted: true,\n                    contentHash: '0x5678...'\n                },\n                {\n                    id: '3',\n                    sender: contactAddress,\n                    content: 'Only you can decrypt and read this message!',\n                    timestamp: new Date(Date.now() - 900000),\n                    isDecrypted: true,\n                    contentHash: '0x9abc...'\n                }\n            ];\n            setMessages(simulatedMessages);\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const decryptMessageContent = async (messageHash)=>{\n        try {\n            // In a real implementation, this would:\n            // 1. Retrieve the encrypted content from off-chain storage using the hash\n            // 2. Decrypt it using the recipient's private key\n            const decryptedContent = await (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_3__.decryptMessage)(messageHash, account?.getAddress().toString() || '');\n            return decryptedContent;\n        } catch (error) {\n            console.error('Failed to decrypt message:', error);\n            return 'Failed to decrypt message';\n        }\n    };\n    const formatTime = (timestamp)=>{\n        const now = new Date();\n        const diff = now.getTime() - timestamp.getTime();\n        const minutes = Math.floor(diff / 60000);\n        const hours = Math.floor(diff / 3600000);\n        const days = Math.floor(diff / ********);\n        if (minutes < 1) return 'Just now';\n        if (minutes < 60) return `${minutes}m ago`;\n        if (hours < 24) return `${hours}h ago`;\n        return `${days}d ago`;\n    };\n    const isOwnMessage = (sender)=>{\n        return sender === account?.getAddress().toString();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"animate-spin h-8 w-8 mx-auto mb-2\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                className: \"opacity-25\",\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                className: \"opacity-75\",\n                                fill: \"currentColor\",\n                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    \"Loading messages...\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n        children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center text-gray-400 mt-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-12 h-12 mx-auto mb-4 opacity-50\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No messages yet\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm mt-1\",\n                    children: \"Start a conversation!\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n            lineNumber: 118,\n            columnNumber: 9\n        }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex ${isOwnMessage(message.sender) ? 'justify-end' : 'justify-start'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${isOwnMessage(message.sender) ? 'bg-purple-600 text-white' : 'bg-white/10 text-white'}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs opacity-75\",\n                                    children: isOwnMessage(message.sender) ? 'You' : 'Contact'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs opacity-75 ml-2\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 15\n                        }, this),\n                        message.isDecrypted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mt-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-3 h-3 text-green-400 mr-1\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs opacity-75\",\n                                    children: \"Decrypted\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 13\n                }, this)\n            }, message.id, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n                lineNumber: 127,\n                columnNumber: 11\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessageInbox.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessageInbox.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessagingInterface.tsx":
/*!***********************************************!*\
  !*** ./src/components/MessagingInterface.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessagingInterface: () => (/* binding */ MessagingInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* harmony import */ var _MessageComposer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageComposer */ \"(ssr)/./src/components/MessageComposer.tsx\");\n/* harmony import */ var _MessageInbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MessageInbox */ \"(ssr)/./src/components/MessageInbox.tsx\");\n/* harmony import */ var _ContactList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ContactList */ \"(ssr)/./src/components/ContactList.tsx\");\n/* harmony import */ var _KeyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./KeyManager */ \"(ssr)/./src/components/KeyManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessagingInterface auto */ \n\n\n\n\n\n\nfunction MessagingInterface() {\n    const { account, disconnect } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('messages');\n    const [selectedContact, setSelectedContact] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const tabs = [\n        {\n            id: 'messages',\n            label: 'Messages',\n            icon: '💬'\n        },\n        {\n            id: 'contacts',\n            label: 'Contacts',\n            icon: '👥'\n        },\n        {\n            id: 'keys',\n            label: 'Keys',\n            icon: '🔐'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto bg-white/10 backdrop-blur-md rounded-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 border-b border-white/10 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-semibold\",\n                                        children: account?.getAddress().toString().slice(0, 2).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-white font-semibold\",\n                                            children: \"MessAge\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: [\n                                                account?.getAddress().toString().slice(0, 10),\n                                                \"...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: disconnect,\n                            className: \"text-gray-400 hover:text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 border-b border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex\",\n                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: `flex-1 px-4 py-3 text-sm font-medium transition-colors ${activeTab === tab.id ? 'text-white bg-white/10 border-b-2 border-purple-500' : 'text-gray-400 hover:text-white hover:bg-white/5'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: tab.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this),\n                                tab.label\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-96 flex\",\n                children: [\n                    activeTab === 'messages' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-1/3 border-r border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContactList__WEBPACK_IMPORTED_MODULE_5__.ContactList, {\n                                    onSelectContact: setSelectedContact,\n                                    selectedContact: selectedContact\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-col\",\n                                children: selectedContact ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInbox__WEBPACK_IMPORTED_MODULE_4__.MessageInbox, {\n                                            contactAddress: selectedContact\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageComposer__WEBPACK_IMPORTED_MODULE_3__.MessageComposer, {\n                                            recipientAddress: selectedContact\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex items-center justify-center text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 mx-auto mb-4 opacity-50\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Select a contact to start messaging\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    activeTab === 'contacts' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContactList__WEBPACK_IMPORTED_MODULE_5__.ContactList, {\n                            onSelectContact: setSelectedContact,\n                            selectedContact: selectedContact,\n                            showManagement: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KeyManager__WEBPACK_IMPORTED_MODULE_6__.KeyManager, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/MessagingInterface.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9NZXNzYWdpbmdJbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0c7QUFDSztBQUNOO0FBQ0Y7QUFDRjtBQUVuQyxTQUFTTTtJQUNkLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxVQUFVLEVBQUUsR0FBR1AsNERBQVFBO0lBQ3hDLE1BQU0sQ0FBQ1EsV0FBV0MsYUFBYSxHQUFHViwrQ0FBUUEsQ0FBbUM7SUFDN0UsTUFBTSxDQUFDVyxpQkFBaUJDLG1CQUFtQixHQUFHWiwrQ0FBUUEsQ0FBZ0I7SUFFdEUsTUFBTWEsT0FBTztRQUNYO1lBQUVDLElBQUk7WUFBWUMsT0FBTztZQUFZQyxNQUFNO1FBQUs7UUFDaEQ7WUFBRUYsSUFBSTtZQUFZQyxPQUFPO1lBQVlDLE1BQU07UUFBSztRQUNoRDtZQUFFRixJQUFJO1lBQVFDLE9BQU87WUFBUUMsTUFBTTtRQUFLO0tBQ3pDO0lBRUQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDQzt3Q0FBS0QsV0FBVTtrREFDYlgsU0FBU2EsYUFBYUMsV0FBV0MsTUFBTSxHQUFHLEdBQUdDOzs7Ozs7Ozs7Ozs4Q0FHbEQsOERBQUNOOztzREFDQyw4REFBQ087NENBQUdOLFdBQVU7c0RBQTJCOzs7Ozs7c0RBQ3pDLDhEQUFDTzs0Q0FBRVAsV0FBVTs7Z0RBQ1ZYLFNBQVNhLGFBQWFDLFdBQVdDLE1BQU0sR0FBRztnREFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJckQsOERBQUNJOzRCQUNDQyxTQUFTbkI7NEJBQ1RVLFdBQVU7c0NBRVYsNEVBQUNVO2dDQUFJVixXQUFVO2dDQUFVVyxNQUFLO2dDQUFPQyxRQUFPO2dDQUFlQyxTQUFROzBDQUNqRSw0RUFBQ0M7b0NBQUtDLGVBQWM7b0NBQVFDLGdCQUFlO29DQUFRQyxhQUFhO29DQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzdFLDhEQUFDbkI7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNtQjtvQkFBSW5CLFdBQVU7OEJBQ1pMLEtBQUt5QixHQUFHLENBQUMsQ0FBQ0Msb0JBQ1QsOERBQUNiOzRCQUVDQyxTQUFTLElBQU1qQixhQUFhNkIsSUFBSXpCLEVBQUU7NEJBQ2xDSSxXQUFXLENBQUMsdURBQXVELEVBQ2pFVCxjQUFjOEIsSUFBSXpCLEVBQUUsR0FDaEIsd0RBQ0EsbURBQ0o7OzhDQUVGLDhEQUFDSztvQ0FBS0QsV0FBVTs4Q0FBUXFCLElBQUl2QixJQUFJOzs7Ozs7Z0NBQy9CdUIsSUFBSXhCLEtBQUs7OzJCQVRMd0IsSUFBSXpCLEVBQUU7Ozs7Ozs7Ozs7Ozs7OzswQkFnQm5CLDhEQUFDRztnQkFBSUMsV0FBVTs7b0JBQ1pULGNBQWMsNEJBQ2I7OzBDQUNFLDhEQUFDUTtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2QscURBQVdBO29DQUNWb0MsaUJBQWlCNUI7b0NBQ2pCRCxpQkFBaUJBOzs7Ozs7Ozs7OzswQ0FHckIsOERBQUNNO2dDQUFJQyxXQUFVOzBDQUNaUCxnQ0FDQzs7c0RBQ0UsOERBQUNSLHVEQUFZQTs0Q0FBQ3NDLGdCQUFnQjlCOzs7Ozs7c0RBQzlCLDhEQUFDVCw2REFBZUE7NENBQUN3QyxrQkFBa0IvQjs7Ozs7OztpRUFHckMsOERBQUNNO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNVO2dEQUFJVixXQUFVO2dEQUFvQ1csTUFBSztnREFBT0MsUUFBTztnREFBZUMsU0FBUTswREFDM0YsNEVBQUNDO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBYTtvREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7MERBRXZFLDhEQUFDWDswREFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVFkaEIsY0FBYyw0QkFDYiw4REFBQ1E7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNkLHFEQUFXQTs0QkFDVm9DLGlCQUFpQjVCOzRCQUNqQkQsaUJBQWlCQTs0QkFDakJnQyxnQkFBZ0I7Ozs7Ozs7Ozs7O29CQUtyQmxDLGNBQWMsd0JBQ2IsOERBQUNRO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDYixtREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNdkIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYmR1bGxhaGlhYmRpL0RldmVsb3Blci9tZXNzLWFnZS9mcm9udGVuZC9zcmMvY29tcG9uZW50cy9NZXNzYWdpbmdJbnRlcmZhY2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUF6dGVjIH0gZnJvbSAnQC9saWIvYXp0ZWMtY29udGV4dCc7XG5pbXBvcnQgeyBNZXNzYWdlQ29tcG9zZXIgfSBmcm9tICcuL01lc3NhZ2VDb21wb3Nlcic7XG5pbXBvcnQgeyBNZXNzYWdlSW5ib3ggfSBmcm9tICcuL01lc3NhZ2VJbmJveCc7XG5pbXBvcnQgeyBDb250YWN0TGlzdCB9IGZyb20gJy4vQ29udGFjdExpc3QnO1xuaW1wb3J0IHsgS2V5TWFuYWdlciB9IGZyb20gJy4vS2V5TWFuYWdlcic7XG5cbmV4cG9ydCBmdW5jdGlvbiBNZXNzYWdpbmdJbnRlcmZhY2UoKSB7XG4gIGNvbnN0IHsgYWNjb3VudCwgZGlzY29ubmVjdCB9ID0gdXNlQXp0ZWMoKTtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlPCdtZXNzYWdlcycgfCAnY29udGFjdHMnIHwgJ2tleXMnPignbWVzc2FnZXMnKTtcbiAgY29uc3QgW3NlbGVjdGVkQ29udGFjdCwgc2V0U2VsZWN0ZWRDb250YWN0XSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IHRhYnMgPSBbXG4gICAgeyBpZDogJ21lc3NhZ2VzJywgbGFiZWw6ICdNZXNzYWdlcycsIGljb246ICfwn5KsJyB9LFxuICAgIHsgaWQ6ICdjb250YWN0cycsIGxhYmVsOiAnQ29udGFjdHMnLCBpY29uOiAn8J+RpScgfSxcbiAgICB7IGlkOiAna2V5cycsIGxhYmVsOiAnS2V5cycsIGljb246ICfwn5SQJyB9LFxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy02eGwgbXgtYXV0byBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLW1kIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS81IGJvcmRlci1iIGJvcmRlci13aGl0ZS8xMCBwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctcHVycGxlLTUwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICAgICAge2FjY291bnQ/LmdldEFkZHJlc3MoKS50b1N0cmluZygpLnNsaWNlKDAsIDIpLnRvVXBwZXJDYXNlKCl9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiPk1lc3NBZ2U8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICB7YWNjb3VudD8uZ2V0QWRkcmVzcygpLnRvU3RyaW5nKCkuc2xpY2UoMCwgMTApfS4uLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtkaXNjb25uZWN0fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTE3IDE2bDQtNG0wIDBsLTQtNG00IDRIN202IDR2MWEzIDMgMCAwMS0zIDNINmEzIDMgMCAwMS0zLTNWN2EzIDMgMCAwMTMtM2g0YTMgMyAwIDAxMyAzdjFcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBOYXZpZ2F0aW9uIFRhYnMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYm9yZGVyLWIgYm9yZGVyLXdoaXRlLzEwXCI+XG4gICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleFwiPlxuICAgICAgICAgIHt0YWJzLm1hcCgodGFiKSA9PiAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIGtleT17dGFiLmlkfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIodGFiLmlkIGFzIGFueSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweC00IHB5LTMgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gdGFiLmlkXG4gICAgICAgICAgICAgICAgICA/ICd0ZXh0LXdoaXRlIGJnLXdoaXRlLzEwIGJvcmRlci1iLTIgYm9yZGVyLXB1cnBsZS01MDAnXG4gICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUvNSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTJcIj57dGFiLmljb259PC9zcGFuPlxuICAgICAgICAgICAgICB7dGFiLmxhYmVsfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvbmF2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW50IEFyZWEgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOTYgZmxleFwiPlxuICAgICAgICB7YWN0aXZlVGFiID09PSAnbWVzc2FnZXMnICYmIChcbiAgICAgICAgICA8PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEvMyBib3JkZXItciBib3JkZXItd2hpdGUvMTBcIj5cbiAgICAgICAgICAgICAgPENvbnRhY3RMaXN0XG4gICAgICAgICAgICAgICAgb25TZWxlY3RDb250YWN0PXtzZXRTZWxlY3RlZENvbnRhY3R9XG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRDb250YWN0PXtzZWxlY3RlZENvbnRhY3R9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgICAgICAge3NlbGVjdGVkQ29udGFjdCA/IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPE1lc3NhZ2VJbmJveCBjb250YWN0QWRkcmVzcz17c2VsZWN0ZWRDb250YWN0fSAvPlxuICAgICAgICAgICAgICAgICAgPE1lc3NhZ2VDb21wb3NlciByZWNpcGllbnRBZGRyZXNzPXtzZWxlY3RlZENvbnRhY3R9IC8+XG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMTIgaC0xMiBteC1hdXRvIG1iLTQgb3BhY2l0eS01MFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk04IDEyaC4wMU0xMiAxMmguMDFNMTYgMTJoLjAxTTIxIDEyYzAgNC40MTgtMy41ODIgOC04IDhhOC45NTkgOC45NTkgMCAwMS00LjkwNi0xLjQ3NkwzIDIxbDIuNDc2LTUuMDk0QTguOTU5IDguOTU5IDAgMDEzIDEyYzAtNC40MTggMy41ODItOCA4LThzOCAzLjU4MiA4IDh6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDxwPlNlbGVjdCBhIGNvbnRhY3QgdG8gc3RhcnQgbWVzc2FnaW5nPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8Lz5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnY29udGFjdHMnICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgPENvbnRhY3RMaXN0XG4gICAgICAgICAgICAgIG9uU2VsZWN0Q29udGFjdD17c2V0U2VsZWN0ZWRDb250YWN0fVxuICAgICAgICAgICAgICBzZWxlY3RlZENvbnRhY3Q9e3NlbGVjdGVkQ29udGFjdH1cbiAgICAgICAgICAgICAgc2hvd01hbmFnZW1lbnQ9e3RydWV9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdrZXlzJyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgIDxLZXlNYW5hZ2VyIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUF6dGVjIiwiTWVzc2FnZUNvbXBvc2VyIiwiTWVzc2FnZUluYm94IiwiQ29udGFjdExpc3QiLCJLZXlNYW5hZ2VyIiwiTWVzc2FnaW5nSW50ZXJmYWNlIiwiYWNjb3VudCIsImRpc2Nvbm5lY3QiLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJzZWxlY3RlZENvbnRhY3QiLCJzZXRTZWxlY3RlZENvbnRhY3QiLCJ0YWJzIiwiaWQiLCJsYWJlbCIsImljb24iLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwiZ2V0QWRkcmVzcyIsInRvU3RyaW5nIiwic2xpY2UiLCJ0b1VwcGVyQ2FzZSIsImgyIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJuYXYiLCJtYXAiLCJ0YWIiLCJvblNlbGVjdENvbnRhY3QiLCJjb250YWN0QWRkcmVzcyIsInJlY2lwaWVudEFkZHJlc3MiLCJzaG93TWFuYWdlbWVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessagingInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/WalletConnection.tsx":
/*!*********************************************!*\
  !*** ./src/components/WalletConnection.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletConnection: () => (/* binding */ WalletConnection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aztec-context */ \"(ssr)/./src/lib/aztec-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ WalletConnection auto */ \n\n\nfunction WalletConnection({ onConnect }) {\n    const { connect } = (0,_lib_aztec_context__WEBPACK_IMPORTED_MODULE_2__.useAztec)();\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleConnect = async ()=>{\n        setIsConnecting(true);\n        try {\n            await connect();\n            onConnect();\n        } catch (error) {\n            console.error('Connection failed:', error);\n        } finally{\n            setIsConnecting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-md mx-auto bg-white/10 backdrop-blur-md rounded-lg p-8 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-white\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-2\",\n                        children: \"Connect to Aztec\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: \"Connect to the Aztec network to start sending private messages\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleConnect,\n                disabled: isConnecting,\n                className: \"w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center\",\n                children: isConnecting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        \"Connecting...\"\n                    ]\n                }, void 0, true) : 'Connect Wallet'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 text-sm text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2\",\n                        children: \"Make sure you have:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-left space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Aztec sandbox running on localhost:8080\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• A compatible wallet or account\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/components/WalletConnection.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/WalletConnection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/aztec-context.tsx":
/*!***********************************!*\
  !*** ./src/lib/aztec-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AztecProvider: () => (/* binding */ AztecProvider),\n/* harmony export */   useAztec: () => (/* binding */ useAztec)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AztecProvider,useAztec auto */ \n\nconst AztecContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AztecProvider({ children }) {\n    const [pxe, setPxe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contractAddress, setContractAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const connect = async ()=>{\n        try {\n            // Simulate connection to Aztec sandbox\n            console.log('Connecting to Aztec sandbox...');\n            // Simulate delay\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Create mock PXE client\n            const mockPxe = {\n                url: 'http://localhost:8080',\n                isConnected: true\n            };\n            setPxe(mockPxe);\n            // Create mock account\n            const mockAccount = {\n                getAddress: ()=>({\n                        toString: ()=>'0x' + Math.random().toString(16).slice(2, 42).padStart(40, '0')\n                    })\n            };\n            setAccount(mockAccount);\n            setIsConnected(true);\n            // Set mock contract address\n            setContractAddress('0x' + Math.random().toString(16).slice(2, 42).padStart(40, '0'));\n            console.log('Connected to Aztec network (simulated)');\n        } catch (error) {\n            console.error('Failed to connect to Aztec:', error);\n            alert('Failed to connect to Aztec network. This is a demo version.');\n        }\n    };\n    const disconnect = ()=>{\n        setPxe(null);\n        setAccount(null);\n        setIsConnected(false);\n        setContractAddress(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AztecContext.Provider, {\n        value: {\n            pxe,\n            account,\n            isConnected,\n            connect,\n            disconnect,\n            contractAddress\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/mess-age/frontend/src/lib/aztec-context.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\nfunction useAztec() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AztecContext);\n    if (context === undefined) {\n        throw new Error('useAztec must be used within an AztecProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/aztec-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/crypto.ts":
/*!***************************!*\
  !*** ./src/lib/crypto.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decryptFromStorage: () => (/* binding */ decryptFromStorage),\n/* harmony export */   decryptMessage: () => (/* binding */ decryptMessage),\n/* harmony export */   deriveSharedSecret: () => (/* binding */ deriveSharedSecret),\n/* harmony export */   encryptForStorage: () => (/* binding */ encryptForStorage),\n/* harmony export */   encryptMessage: () => (/* binding */ encryptMessage),\n/* harmony export */   exportPublicKey: () => (/* binding */ exportPublicKey),\n/* harmony export */   generateKeyPair: () => (/* binding */ generateKeyPair),\n/* harmony export */   generateKeyPairFromSeed: () => (/* binding */ generateKeyPairFromSeed),\n/* harmony export */   generateMessageProof: () => (/* binding */ generateMessageProof),\n/* harmony export */   generateNonce: () => (/* binding */ generateNonce),\n/* harmony export */   hashMessage: () => (/* binding */ hashMessage),\n/* harmony export */   isValidAztecAddress: () => (/* binding */ isValidAztecAddress),\n/* harmony export */   signMessage: () => (/* binding */ signMessage),\n/* harmony export */   verifyMessageProof: () => (/* binding */ verifyMessageProof),\n/* harmony export */   verifySignature: () => (/* binding */ verifySignature)\n/* harmony export */ });\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto-js */ \"(ssr)/./node_modules/crypto-js/index.js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Generate a new key pair for encryption\nasync function generateKeyPair() {\n    // In a real implementation, this would use proper elliptic curve cryptography\n    // For demo purposes, we'll use a simplified approach\n    const privateKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().lib.WordArray.random(32).toString();\n    const publicKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(privateKey).toString();\n    return {\n        publicKey: `0x${publicKey}`,\n        privateKey: `0x${privateKey}`\n    };\n}\n// Export public key in a shareable format\nfunction exportPublicKey(publicKey) {\n    return publicKey;\n}\n// Encrypt a message using the recipient's public key\nasync function encryptMessage(message, recipientPublicKey) {\n    try {\n        // In a real implementation, this would use ECIES (Elliptic Curve Integrated Encryption Scheme)\n        // For demo purposes, we'll use AES encryption with a derived key\n        const derivedKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(recipientPublicKey).toString();\n        const encrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.encrypt(message, derivedKey).toString();\n        return encrypted;\n    } catch (error) {\n        console.error('Encryption failed:', error);\n        throw new Error('Failed to encrypt message');\n    }\n}\n// Decrypt a message using the recipient's private key\nasync function decryptMessage(encryptedMessage, privateKey) {\n    try {\n        // In a real implementation, this would use the private key to decrypt ECIES-encrypted data\n        // For demo purposes, we'll use the private key to derive the AES key\n        const derivedKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(privateKey).toString();\n        const decrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encryptedMessage, derivedKey);\n        const decryptedText = decrypted.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n        if (!decryptedText) {\n            throw new Error('Decryption failed - invalid key or corrupted data');\n        }\n        return decryptedText;\n    } catch (error) {\n        console.error('Decryption failed:', error);\n        throw new Error('Failed to decrypt message');\n    }\n}\n// Create a hash of the message content\nfunction hashMessage(content) {\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(content).toString();\n}\n// Generate a zero-knowledge proof for message ownership\nasync function generateMessageProof(message, privateKey, recipientPublicKey) {\n    // In a real implementation, this would generate a ZK-SNARK proof\n    // proving that the sender knows the message content without revealing it\n    const messageHash = hashMessage(message);\n    const commitment = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(messageHash + privateKey).toString();\n    // Simplified proof structure\n    const proof = {\n        proof: `0x${commitment}`,\n        publicSignals: [\n            messageHash,\n            crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(privateKey).toString(),\n            recipientPublicKey\n        ]\n    };\n    return proof;\n}\n// Verify a zero-knowledge proof\nasync function verifyMessageProof(proof, expectedMessageHash) {\n    try {\n        // In a real implementation, this would verify a ZK-SNARK proof\n        // For demo purposes, we'll do a simple verification\n        const [messageHash, senderPublicKey, recipientPublicKey] = proof.publicSignals;\n        // Verify that the message hash matches\n        if (messageHash !== expectedMessageHash) {\n            return false;\n        }\n        // Verify proof structure (simplified)\n        const expectedCommitment = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(messageHash + 'dummy_private_key').toString();\n        return proof.proof.includes(messageHash.slice(0, 8));\n    } catch (error) {\n        console.error('Proof verification failed:', error);\n        return false;\n    }\n}\n// Generate a secure random nonce\nfunction generateNonce() {\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().lib.WordArray.random(16).toString();\n}\n// Derive a shared secret between two parties (simplified ECDH)\nasync function deriveSharedSecret(privateKey, publicKey) {\n    // In a real implementation, this would use ECDH key agreement\n    // For demo purposes, we'll use a simple hash combination\n    const combined = privateKey + publicKey;\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(combined).toString();\n}\n// Encrypt data for storage (using a password)\nfunction encryptForStorage(data, password) {\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.encrypt(data, password).toString();\n}\n// Decrypt data from storage\nfunction decryptFromStorage(encryptedData, password) {\n    const decrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encryptedData, password);\n    return decrypted.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n}\n// Validate an Aztec address format\nfunction isValidAztecAddress(address) {\n    // Basic validation for Aztec address format\n    return /^0x[a-fA-F0-9]{40}$/.test(address);\n}\n// Generate a deterministic key pair from a seed\nasync function generateKeyPairFromSeed(seed) {\n    const privateKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(seed).toString();\n    const publicKey = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(privateKey + 'public').toString();\n    return {\n        publicKey: `0x${publicKey}`,\n        privateKey: `0x${privateKey}`\n    };\n}\n// Create a message signature for authentication\nfunction signMessage(message, privateKey) {\n    const messageHash = hashMessage(message);\n    const signature = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().HmacSHA256(messageHash, privateKey).toString();\n    return `0x${signature}`;\n}\n// Verify a message signature\nfunction verifySignature(message, signature, publicKey) {\n    try {\n        const messageHash = hashMessage(message);\n        // In a real implementation, this would verify an ECDSA signature\n        // For demo purposes, we'll use a simplified verification\n        return signature.length === 66 && signature.startsWith('0x');\n    } catch (error) {\n        console.error('Signature verification failed:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/crypto.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/crypto-js","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fabdullahiabdi%2FDeveloper%2Fmess-age%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();